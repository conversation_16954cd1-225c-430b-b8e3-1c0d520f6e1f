<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>REST Client</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/pagination.css">
</head>
<body>
  <div class="app">
    <nav class="navbar">
      <div class="logo">REST Client</div>
      <div class="user-area">
        <span class="username" id="username">Guest</span>
        <button id="login-btn" class="btn">Login</button>
        <button id="register-btn" class="btn">Register</button>
        <button id="logout-btn" class="btn hidden">Logout</button>
      </div>
    </nav>

    <div class="main-container">
      <aside class="sidebar">
        <div class="sidebar-tabs">
          <button class="sidebar-tab active" data-tab="collections">Collections</button>
          <button class="sidebar-tab" data-tab="history">History</button>
        </div>

        <div id="collections-tab" class="sidebar-content active">
          <div class="sidebar-header">
            <h3>Collections</h3>
            <button id="add-collection-btn" class="btn-small"><i class="fas fa-plus"></i></button>
          </div>

          <div class="workspace-selector">
            <label for="workspace-select">Workspace:</label>
            <select id="workspace-select">
              <option value="1">Default Workspace</option>
              <option value="2">Personal Workspace</option>
              <option value="3">Team Workspace</option>
              <option value="all">All Workspaces</option>
            </select>
          </div>

          <div id="collections-list" class="collections-list">
            <!-- Collections will be rendered here -->
          </div>
        </div>

        <div id="history-tab" class="sidebar-content">
          <div class="sidebar-header">
            <h3>History</h3>
            <button id="clear-history-btn" class="btn-small" title="Clear History"><i class="fas fa-trash"></i></button>
          </div>
          <div id="history-list" class="history-list">
            <!-- History items will be rendered here -->
          </div>
        </div>
      </aside>

      <main class="content">
        <div id="auth-container" class="auth-container">
          <h2>Welcome to REST Client</h2>
          <p>Login or register to start managing your API requests</p>
        </div>

        <div id="requests-container" class="requests-container hidden">
          <div class="request-editor">
            <div class="request-header">
              <input type="text" id="request-name" placeholder="Request Name" class="request-name">
              <div class="request-controls">
                <select id="request-method" class="request-method">
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                  <option value="PATCH">PATCH</option>
                </select>
                <input type="text" id="request-url" placeholder="Enter URL" class="request-url">
                <button id="send-btn" class="btn">Send</button>
                <button id="save-btn" class="btn">Save</button>
              </div>
            </div>

            <div class="request-body">
              <div class="tabs">
                <button class="tab-btn active" data-tab="params">Params</button>
                <button class="tab-btn" data-tab="authorization">Authorization</button>
                <button class="tab-btn" data-tab="headers">Headers</button>
                <button class="tab-btn" data-tab="body">Body</button>
              </div>

              <div id="params-tab" class="tab-content active">
                <table class="params-table">
                  <thead>
                    <tr>
                      <th>Key</th>
                      <th>Value</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody id="params-body">
                    <tr>
                      <td><input type="text" class="param-key"></td>
                      <td><input type="text" class="param-value"></td>
                      <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
                    </tr>
                  </tbody>
                </table>
                <button id="add-param-btn" class="btn-small">Add Parameter</button>
              </div>

              <div id="authorization-tab" class="tab-content">
                <div class="auth-type-selector">
                  <label for="auth-type">Type</label>
                  <select id="auth-type" class="auth-type">
                    <option value="none">No Auth</option>
                    <option value="basic">Basic Auth</option>
                    <option value="bearer">Bearer Token</option>
                    <option value="api-key">API Key</option>
                    <option value="oauth2">OAuth 2.0</option>
                  </select>
                </div>

                <div id="auth-none" class="auth-form active">
                  <p>This request does not use any authorization.</p>
                </div>

                <div id="auth-basic" class="auth-form">
                  <div class="form-group">
                    <label for="basic-username">Username</label>
                    <input type="text" id="basic-username" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="basic-password">Password</label>
                    <input type="password" id="basic-password" class="auth-input">
                  </div>
                </div>

                <div id="auth-bearer" class="auth-form">
                  <div class="form-group">
                    <label for="bearer-token">Token</label>
                    <input type="text" id="bearer-token" class="auth-input">
                  </div>
                </div>

                <div id="auth-api-key" class="auth-form">
                  <div class="form-group">
                    <label for="api-key-name">Key</label>
                    <input type="text" id="api-key-name" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="api-key-value">Value</label>
                    <input type="text" id="api-key-value" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="api-key-in">Add to</label>
                    <select id="api-key-in" class="auth-input">
                      <option value="header">Header</option>
                      <option value="query">Query Params</option>
                    </select>
                  </div>
                </div>

                <div id="auth-oauth2" class="auth-form">
                  <div class="form-group">
                    <label for="oauth2-token-url">Token URL</label>
                    <input type="text" id="oauth2-token-url" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="oauth2-client-id">Client ID</label>
                    <input type="text" id="oauth2-client-id" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="oauth2-client-secret">Client Secret</label>
                    <input type="text" id="oauth2-client-secret" class="auth-input">
                  </div>
                  <div class="form-group">
                    <label for="oauth2-scope">Scope</label>
                    <input type="text" id="oauth2-scope" class="auth-input">
                  </div>
                  <button id="oauth2-get-token" class="btn">Get New Access Token</button>
                </div>
              </div>

              <div id="headers-tab" class="tab-content">
                <table class="headers-table">
                  <thead>
                    <tr>
                      <th>Key</th>
                      <th>Value</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody id="headers-body">
                    <tr>
                      <td><input type="text" class="header-key"></td>
                      <td><input type="text" class="header-value"></td>
                      <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
                    </tr>
                  </tbody>
                </table>
                <button id="add-header-btn" class="btn-small">Add Header</button>
              </div>

              <div id="body-tab" class="tab-content">
                <div class="body-type-selector">
                  <select id="body-type">
                    <option value="none">None</option>
                    <option value="form-data">Form Data</option>
                    <option value="x-www-form-urlencoded">x-www-form-urlencoded</option>
                    <option value="raw">Raw</option>
                  </select>
                </div>

                <div id="form-data-container" class="body-container hidden">
                  <table class="form-data-table">
                    <thead>
                      <tr>
                        <th>Key</th>
                        <th>Value</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody id="form-data-body">
                      <tr>
                        <td><input type="text" class="form-key"></td>
                        <td><input type="text" class="form-value"></td>
                        <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
                      </tr>
                    </tbody>
                  </table>
                  <button id="add-form-btn" class="btn-small">Add Form Field</button>
                </div>

                <div id="urlencoded-container" class="body-container hidden">
                  <table class="urlencoded-table">
                    <thead>
                      <tr>
                        <th>Key</th>
                        <th>Value</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody id="urlencoded-body">
                      <tr>
                        <td><input type="text" class="urlencoded-key"></td>
                        <td><input type="text" class="urlencoded-value"></td>
                        <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
                      </tr>
                    </tbody>
                  </table>
                  <button id="add-urlencoded-btn" class="btn-small">Add URL Encoded Field</button>
                </div>

                <div id="raw-container" class="body-container hidden">
                  <select id="raw-type">
                    <option value="text">Text</option>
                    <option value="json">JSON</option>
                    <option value="xml">XML</option>
                    <option value="html">HTML</option>
                  </select>
                  <textarea id="raw-body" class="raw-body"></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="response-viewer">
            <div class="response-header">
              <h3>Response</h3>
              <div class="response-meta">
                <span id="status-code" class="status-code">200 OK</span>
                <span id="response-time" class="response-time">0 ms</span>
              </div>
            </div>

            <div class="response-body">
              <div class="tabs">
                <button class="tab-btn active" data-tab="response-body">Body</button>
                <button class="tab-btn" data-tab="response-headers">Headers</button>
              </div>

              <div id="response-body-tab" class="tab-content active">
                <pre id="response-body-content" class="response-body-content"></pre>
              </div>

              <div id="response-headers-tab" class="tab-content">
                <table class="response-headers-table">
                  <thead>
                    <tr>
                      <th>Key</th>
                      <th>Value</th>
                    </tr>
                  </thead>
                  <tbody id="response-headers-body">
                </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
</div>

<!-- Modals -->
<div id="login-modal" class="modal">
<div class="modal-content">
  <span class="close">&times;</span>
  <h2>Login</h2>
  <form id="login-form">
    <div class="form-group">
      <label for="login-email">Email</label>
      <input type="email" id="login-email" required>
    </div>
    <div class="form-group">
      <label for="login-password">Password</label>
      <input type="password" id="login-password" required>
    </div>
    <button type="submit" class="btn">Login</button>
  </form>
</div>
</div>

<div id="register-modal" class="modal">
<div class="modal-content">
  <span class="close">&times;</span>
  <h2>Register</h2>
  <form id="register-form">
    <div class="form-group">
      <label for="register-name">Name</label>
      <input type="text" id="register-name" required>
    </div>
    <div class="form-group">
      <label for="register-email">Email</label>
      <input type="email" id="register-email" required>
    </div>
    <div class="form-group">
      <label for="register-password">Password</label>
      <input type="password" id="register-password" required>
    </div>
    <div class="form-group">
      <label for="register-confirm-password">Confirm Password</label>
      <input type="password" id="register-confirm-password" required>
    </div>
    <button type="submit" class="btn">Register</button>
  </form>
</div>
</div>

<div id="collection-modal" class="modal">
<div class="modal-content">
  <span class="close">&times;</span>
  <h2>Create Collection</h2>
  <form id="collection-form">
    <div class="form-group">
      <label for="collection-name">Name</label>
      <input type="text" id="collection-name" required>
    </div>
    <div class="form-group">
      <label for="collection-description">Description</label>
      <textarea id="collection-description"></textarea>
    </div>
    <div class="form-group">
      <label for="collection-workspace">Workspace</label>
      <select id="collection-workspace">
        <option value="1">Default Workspace</option>
        <option value="2">Personal Workspace</option>
        <option value="3">Team Workspace</option>
      </select>
    </div>
    <button type="submit" class="btn">Create</button>
  </form>
</div>
</div>

<!-- Rename Request Modal -->
<div id="rename-request-modal" class="modal">
<div class="modal-content">
  <span class="close">&times;</span>
  <h2>Rename Request</h2>
  <form id="rename-request-form">
    <div class="form-group">
      <label for="rename-request-name">Name</label>
      <input type="text" id="rename-request-name" required>
      <input type="hidden" id="rename-request-id">
    </div>
    <button type="submit" class="btn">Rename</button>
  </form>
</div>
</div>

<!-- New Request Modal -->
<div id="request-modal" class="modal">
<div class="modal-content">
  <span class="close">&times;</span>
  <h2>Create Request</h2>
  <form id="request-form">
    <div class="form-group">
      <label for="request-modal-name">Name</label>
      <input type="text" id="request-modal-name" placeholder="Enter request name" required>
    </div>
    <input type="hidden" id="request-modal-method" value="GET">
    <input type="hidden" id="request-modal-url" value="https://example.com">
    <input type="hidden" id="request-modal-collection-id">
    <input type="hidden" id="request-modal-folder-id">
    <button type="submit" class="btn">Create</button>
  </form>
</div>
</div>

<script src="/js/main.js"></script>
<script src="/js/collections.js"></script>
<script src="/js/folders.js"></script>
<script src="/js/authorization.js"></script>
<script src="/js/requests.js"></script>
<script src="/js/history.js"></script>
</body>
</html>