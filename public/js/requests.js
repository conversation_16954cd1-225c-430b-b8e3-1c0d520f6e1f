document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM content loaded in requests.js');

    // DOM Elements
    const requestNameInput = document.getElementById('request-name');
    const requestMethodSelect = document.getElementById('request-method');
    const requestUrlInput = document.getElementById('request-url');
    const sendBtn = document.getElementById('send-btn');
    const saveBtn = document.getElementById('save-btn');
    const statusCodeEl = document.getElementById('status-code');
    const responseTimeEl = document.getElementById('response-time');
    const responseBodyContentEl = document.getElementById('response-body-content');
    const responseHeadersBodyEl = document.getElementById('response-headers-body');

    // Request Modal Elements
    const requestModal = document.getElementById('request-modal');
    const requestForm = document.getElementById('request-form');
    const requestModalName = document.getElementById('request-modal-name');
    const requestModalMethod = document.getElementById('request-modal-method');
    const requestModalUrl = document.getElementById('request-modal-url');
    const requestModalCollectionId = document.getElementById('request-modal-collection-id');
    const requestModalFolderId = document.getElementById('request-modal-folder-id');

    // Debug DOM elements
    console.log('DOM Elements:', {
      requestNameInput,
      requestMethodSelect,
      requestUrlInput,
      sendBtn,
      saveBtn,
      statusCodeEl,
      responseTimeEl,
      responseBodyContentEl,
      responseHeadersBodyEl,
      requestModal,
      requestForm
    });

    // Close request modal when clicking on X
    if (requestModal) {
      requestModal.querySelector('.close').addEventListener('click', () => {
        requestModal.style.display = 'none';
      });
    }

    // Close request modal when clicking outside
    window.addEventListener('click', (event) => {
      if (event.target === requestModal) {
        requestModal.style.display = 'none';
      }
    });

    // Request form submission
    if (requestForm) {
      requestForm.addEventListener('submit', event => {
        event.preventDefault();

        const name = requestModalName.value.trim();
        if (!name) {
          alert('Please enter a name for the request');
          requestModalName.focus();
          return;
        }

        // Use default values for method and URL
        const method = requestModalMethod.value || 'GET';
        const url = requestModalUrl.value || 'https://example.com';
        const collectionId = requestModalCollectionId.value;
        const folderId = requestModalFolderId.value;

        const token = localStorage.getItem('token');
        if (!token) {
          console.error('You must be logged in to create a request');
          return;
        }

        // Show a loading indicator
        const submitButton = requestForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;
        submitButton.textContent = 'Creating...';
        submitButton.disabled = true;

        // Get the source of the request (collection or folder)
        const source = requestForm.getAttribute('data-source');
        console.log('Creating request from source:', source);

        // Prepare request data
        const requestData = {
          name,
          method,
          url,
          workspace_id: 123 // Default workspace ID
        };

        // Add collection_id or folder_id based on where the request is being added
        if (source === 'folder' && folderId) {
          console.log('Adding request to folder with ID:', folderId);
          requestData.folder_id = folderId;
        } else if (source === 'collection' && collectionId) {
          console.log('Adding request to collection with ID:', collectionId);
          requestData.collection_id = collectionId;
        } else {
          // Fallback to the previous logic
          if (folderId) {
            console.log('Fallback: Adding request to folder with ID:', folderId);
            requestData.folder_id = folderId;
          } else if (collectionId) {
            console.log('Fallback: Adding request to collection with ID:', collectionId);
            requestData.collection_id = collectionId;
          }
        }

        console.log('Creating request with data:', requestData);

        // Send request to API
        fetch('/api/requests', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          },
          body: JSON.stringify(requestData)
        })
        .then(res => {
          console.log('Request creation response status:', res.status);
          return res.json();
        })
        .then(response => {
          console.log('Request creation response:', response);

          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Extract the request data from the response
          const data = response.data || response;

          if (data && data.id) {
            // Close modal
            requestModal.style.display = 'none';

            // Reset form
            requestForm.reset();

            console.log('Request created successfully. ID:', data.id);

            // Get the source of the request (collection or folder)
            const source = requestForm.getAttribute('data-source');

            // Determine the location message
            let locationMsg = '';
            if (source === 'folder') {
              locationMsg = 'in folder';
            } else if (source === 'collection') {
              locationMsg = 'in collection';
            }

            // Show success notification
            const notification = document.createElement('div');
            notification.className = 'notification success';
            notification.textContent = `Request "${name}" created successfully ${locationMsg}!`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.left = '50%';
            notification.style.transform = 'translateX(-50%)';
            notification.style.padding = '10px 20px';
            notification.style.backgroundColor = 'var(--success-color, #28a745)';
            notification.style.color = 'white';
            notification.style.borderRadius = '4px';
            notification.style.zIndex = '1000';
            notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

            // Add the notification to the body
            document.body.appendChild(notification);

            // Remove the notification after 3 seconds
            setTimeout(() => {
              document.body.removeChild(notification);
            }, 3000);

            // Reload the parent collection or folder
            if (source === 'folder' && folderId) {
              console.log('Reloading folder with ID:', folderId);
              if (window.loadFolder) {
                window.loadFolder(folderId);
              }
            } else if (source === 'collection' && collectionId) {
              console.log('Reloading collection with ID:', collectionId);
              if (window.loadCollection) {
                window.loadCollection(collectionId);
              }
            } else {
              // Fallback to the previous logic
              if (folderId) {
                console.log('Fallback: Reloading folder with ID:', folderId);
                if (window.loadFolder) {
                  window.loadFolder(folderId);
                }
              } else if (collectionId) {
                console.log('Fallback: Reloading collection with ID:', collectionId);
                if (window.loadCollection) {
                  window.loadCollection(collectionId);
                }
              }
            }

            // Load the created request in the editor
            loadRequest(data.id);
          } else {
            console.error(data.message || 'Request creation failed');
            alert('Failed to create request. Please try again.');
          }
        })
        .catch(err => {
          console.error('Error creating request:', err);
          console.error('Request creation failed. Please try again.');

          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Show error message
          alert('Failed to create request: ' + (err.message || 'Unknown error'));
        });
      });
    }

    // Make sure the save button is visible and clickable
    if (saveBtn) {
      saveBtn.style.position = 'relative';
      saveBtn.style.zIndex = '100';
      saveBtn.style.backgroundColor = 'var(--success-color)';
      saveBtn.style.color = 'white';
      saveBtn.style.fontWeight = 'bold';
      saveBtn.style.padding = '10px 20px';
      saveBtn.style.cursor = 'pointer';
      saveBtn.style.border = 'none';
      saveBtn.style.borderRadius = '4px';
      saveBtn.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
      console.log('Save button initialized:', saveBtn);

      // Debug function to check save button attributes
      window.checkSaveButtonAttributes = function() {
        const collectionId = saveBtn.getAttribute('data-collection-id');
        const folderId = saveBtn.getAttribute('data-folder-id');
        const requestId = saveBtn.getAttribute('data-request-id');

        console.log('Save button attributes:', {
          'data-collection-id': collectionId,
          'data-folder-id': folderId,
          'data-request-id': requestId
        });

        // Create a notification to show the attributes
        const notification = document.createElement('div');
        notification.className = 'notification info';
        notification.textContent = `Save button attributes: Collection ID: ${collectionId || 'none'}, Folder ID: ${folderId || 'none'}, Request ID: ${requestId || 'none'}`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.padding = '10px 20px';
        notification.style.backgroundColor = '#007bff';
        notification.style.color = 'white';
        notification.style.borderRadius = '4px';
        notification.style.zIndex = '1000';
        notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Add the notification to the body
        document.body.appendChild(notification);

        // Remove the notification after 5 seconds
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 5000);

        return {
          collectionId,
          folderId,
          requestId
        };
      };

      // Call the debug function once on load
      setTimeout(() => {
        window.checkSaveButtonAttributes();
      }, 2000);
    }

    // Add parameter button
    document.getElementById('add-param-btn').addEventListener('click', () => {
      const paramsBody = document.getElementById('params-body');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td><input type="text" class="param-key"></td>
        <td><input type="text" class="param-value"></td>
        <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
      `;

      // Remove parameter event
      newRow.querySelector('.remove-param').addEventListener('click', () => {
        paramsBody.removeChild(newRow);
      });

      paramsBody.appendChild(newRow);
    });

    // Add header button
    document.getElementById('add-header-btn').addEventListener('click', () => {
      const headersBody = document.getElementById('headers-body');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td><input type="text" class="header-key"></td>
        <td><input type="text" class="header-value"></td>
        <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
      `;

      // Remove header event
      newRow.querySelector('.remove-header').addEventListener('click', () => {
        headersBody.removeChild(newRow);
      });

      headersBody.appendChild(newRow);
    });

    // Add form field button
    document.getElementById('add-form-btn').addEventListener('click', () => {
      const formDataBody = document.getElementById('form-data-body');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td><input type="text" class="form-key"></td>
        <td><input type="text" class="form-value"></td>
        <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
      `;

      // Remove form field event
      newRow.querySelector('.remove-form').addEventListener('click', () => {
        formDataBody.removeChild(newRow);
      });

      formDataBody.appendChild(newRow);
    });

    // Add urlencoded field button
    document.getElementById('add-urlencoded-btn').addEventListener('click', () => {
      const urlencodedBody = document.getElementById('urlencoded-body');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td><input type="text" class="urlencoded-key"></td>
        <td><input type="text" class="urlencoded-value"></td>
        <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
      `;

      // Remove urlencoded field event
      newRow.querySelector('.remove-urlencoded').addEventListener('click', () => {
        urlencodedBody.removeChild(newRow);
      });

      urlencodedBody.appendChild(newRow);
    });

    // Body type change event
    document.getElementById('body-type').addEventListener('change', function() {
      // Hide all body containers
      document.querySelectorAll('.body-container').forEach(container => {
        container.classList.add('hidden');
      });

      // Show selected body container
      const selectedType = this.value;
      if (selectedType === 'form-data') {
        document.getElementById('form-data-container').classList.remove('hidden');
      } else if (selectedType === 'x-www-form-urlencoded') {
        document.getElementById('urlencoded-container').classList.remove('hidden');
      } else if (selectedType === 'raw') {
        document.getElementById('raw-container').classList.remove('hidden');
      }
    });

    // Tab switching functionality
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        // Get the tab to show
        const tabId = this.getAttribute('data-tab');

        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(tab => {
          tab.classList.remove('active');
        });

        // Remove active class from all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });

        // Add active class to clicked tab
        this.classList.add('active');

        // Add active class to corresponding tab content
        document.getElementById(`${tabId}-tab`).classList.add('active');
      });
    });

    // Add request to collection
    window.addRequestToCollection = function(collectionId) {
      console.log('Adding request to collection:', collectionId);

      // Get the modal and form elements directly
      const modal = document.getElementById('request-modal');
      const form = document.getElementById('request-form');
      const nameInput = document.getElementById('request-modal-name');
      const methodInput = document.getElementById('request-modal-method');
      const urlInput = document.getElementById('request-modal-url');
      const collectionIdInput = document.getElementById('request-modal-collection-id');
      const folderIdInput = document.getElementById('request-modal-folder-id');

      if (!modal || !form || !nameInput || !methodInput || !urlInput || !collectionIdInput || !folderIdInput) {
        console.error('Required elements not found for addRequestToCollection');
        console.log('Elements:', { modal, form, nameInput, methodInput, urlInput, collectionIdInput, folderIdInput });
        return;
      }

      // Reset form
      form.reset();

      // Update modal title
      const titleEl = modal.querySelector('h2');
      if (titleEl) {
        titleEl.textContent = 'Create Request in Collection';
      }

      // Set collection ID and clear folder ID
      collectionIdInput.value = collectionId;
      folderIdInput.value = '';

      // Set default values for hidden fields
      methodInput.value = 'GET';
      urlInput.value = 'https://example.com';

      // Add a data attribute to track the source
      form.setAttribute('data-source', 'collection');

      // Focus on the name input
      setTimeout(() => {
        nameInput.focus();
      }, 100);

      // Show modal
      modal.style.display = 'block';
    };

    // Add request to folder
    window.addRequestToFolder = function(folderId) {
      console.log('addRequestToFolder function called with folderId:', folderId);

      try {
        // Get the modal and form elements directly
        const modal = document.getElementById('request-modal');
        console.log('Modal element found:', !!modal);

        const form = document.getElementById('request-form');
        const nameInput = document.getElementById('request-modal-name');
        const methodInput = document.getElementById('request-modal-method');
        const urlInput = document.getElementById('request-modal-url');
        const collectionIdInput = document.getElementById('request-modal-collection-id');
        const folderIdInput = document.getElementById('request-modal-folder-id');

        console.log('Form elements found:', {
          form: !!form,
          nameInput: !!nameInput,
          methodInput: !!methodInput,
          urlInput: !!urlInput,
          collectionIdInput: !!collectionIdInput,
          folderIdInput: !!folderIdInput
        });

        if (!modal || !form || !nameInput || !methodInput || !urlInput || !collectionIdInput || !folderIdInput) {
          console.error('Required elements not found for addRequestToFolder');
          return;
        }

        // Reset form
        form.reset();
        console.log('Form reset');

        // Update modal title
        const titleEl = modal.querySelector('h2');
        if (titleEl) {
          titleEl.textContent = 'Create Request in Folder';
          console.log('Modal title updated');
        }

        // Set folder ID and clear collection ID
        folderIdInput.value = folderId;
        collectionIdInput.value = '';
        console.log('Folder ID set:', folderIdInput.value);

        // Set default values for hidden fields
        methodInput.value = 'GET';
        urlInput.value = 'https://example.com';

        // Add a data attribute to track the source
        form.setAttribute('data-source', 'folder');

        // Focus on the name input
        setTimeout(() => {
          nameInput.focus();
        }, 100);

        // Show modal
        console.log('Setting modal display to block');
        modal.style.display = 'block';

        // Force modal to be visible with inline styles
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
        modal.style.zIndex = '1000';

        console.log('Modal display style after setting:', modal.style.display);

        // Add a class to ensure visibility
        modal.classList.add('visible');

        // Log the current state of the modal
        console.log('Modal current state:', {
          display: modal.style.display,
          opacity: modal.style.opacity,
          visibility: modal.style.visibility,
          zIndex: modal.style.zIndex,
          classList: Array.from(modal.classList)
        });
      } catch (error) {
        console.error('Error in addRequestToFolder function:', error);
      }
    };

    // Helper function to clear the request form completely
    window.clearRequestForm = function() {
      // Clear form fields
      document.getElementById('request-name').value = '';
      document.getElementById('request-url').value = '';
      document.getElementById('request-method').value = 'GET';

      // Clear request ID from both buttons
      const sendBtn = document.getElementById('send-btn');
      const saveBtn = document.getElementById('save-btn');

      if (sendBtn) sendBtn.removeAttribute('data-request-id');
      if (saveBtn) saveBtn.removeAttribute('data-request-id');

      // Clear headers
      const headersBody = document.getElementById('headers-body');
      if (headersBody) {
        headersBody.innerHTML = `
          <tr>
            <td><input type="text" class="header-key"></td>
            <td><input type="text" class="header-value"></td>
            <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
          </tr>
        `;

        // Add event listener for remove header button
        const removeHeaderBtn = headersBody.querySelector('.remove-header');
        if (removeHeaderBtn) {
          removeHeaderBtn.addEventListener('click', function() {
            const row = this.closest('tr');
            if (row && row.parentNode) {
              row.parentNode.removeChild(row);
            }
          });
        }
      }

      // Clear params
      const paramsBody = document.getElementById('params-body');
      if (paramsBody) {
        paramsBody.innerHTML = `
          <tr>
            <td><input type="text" class="param-key"></td>
            <td><input type="text" class="param-value"></td>
            <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
          </tr>
        `;

        // Add event listener for remove param button
        const removeParamBtn = paramsBody.querySelector('.remove-param');
        if (removeParamBtn) {
          removeParamBtn.addEventListener('click', function() {
            const row = this.closest('tr');
            if (row && row.parentNode) {
              row.parentNode.removeChild(row);
            }
          });
        }
      }

      // Reset body type and clear body content
      const bodyType = document.getElementById('body-type');
      if (bodyType) {
        bodyType.value = 'none';

        // Hide all body containers
        document.querySelectorAll('.body-container').forEach(container => {
          container.classList.add('hidden');
        });
      }

      // Clear raw body
      const rawBody = document.getElementById('raw-body');
      if (rawBody) {
        rawBody.value = '';
      }

      // Reset authorization if available
      if (window.setAuthorizationData) {
        window.setAuthorizationData({ type: 'none' });
      }

      console.log('Request form cleared completely');
    };

    // Send request button
    console.log('Setting up send button event listener in requests.js');
    if (!sendBtn) {
      console.error('Send button not found in requests.js');
      return;
    }

    // Add event listener without replacing the button
    sendBtn.addEventListener('click', async () => {
      console.log('Send button clicked in requests.js');
      const method = requestMethodSelect.value;
      const url = requestUrlInput.value;

      if (!url) {
        console.error('Please enter a URL');
        return;
      }

      // Get headers
      const headers = {};
      document.querySelectorAll('#headers-body tr').forEach(row => {
        const key = row.querySelector('.header-key').value;
        const value = row.querySelector('.header-value').value;
        if (key) {
          headers[key] = value;
        }
      });

      // Add authorization headers
      if (window.getAuthorizationHeaders) {
        const authHeaders = window.getAuthorizationHeaders();
        Object.assign(headers, authHeaders);
      }

      // Get body based on the selected type
      let body = null;
      const bodyType = document.getElementById('body-type').value;

      if (method !== 'GET' && bodyType !== 'none') {
        if (bodyType === 'raw') {
          const rawType = document.getElementById('raw-type').value;
          const rawBody = document.getElementById('raw-body').value;

          if (rawType === 'json') {
            try {
              body = JSON.parse(rawBody);
              // Set Content-Type header if not already set
              if (!headers['Content-Type']) {
                headers['Content-Type'] = 'application/json';
              }
            } catch (error) {
              console.error('Invalid JSON body');
              return;
            }
          } else {
            body = rawBody;
            // Set appropriate Content-Type header
            if (!headers['Content-Type']) {
              if (rawType === 'xml') {
                headers['Content-Type'] = 'application/xml';
              } else if (rawType === 'html') {
                headers['Content-Type'] = 'text/html';
              } else {
                headers['Content-Type'] = 'text/plain';
              }
            }
          }
        } else if (bodyType === 'form-data') {
          body = new FormData();
          document.querySelectorAll('#form-data-body tr').forEach(row => {
            const key = row.querySelector('.form-key').value;
            const value = row.querySelector('.form-value').value;
            if (key) {
              body.append(key, value);
            }
          });
        } else if (bodyType === 'x-www-form-urlencoded') {
          const formData = {};
          document.querySelectorAll('#urlencoded-body tr').forEach(row => {
            const key = row.querySelector('.urlencoded-key').value;
            const value = row.querySelector('.urlencoded-value').value;
            if (key) {
              formData[key] = value;
            }
          });

          // Create URL search params
          const urlSearchParams = new URLSearchParams();
          for (const key in formData) {
            urlSearchParams.append(key, formData[key]);
          }

          body = urlSearchParams.toString();

          // Set Content-Type header if not already set
          if (!headers['Content-Type']) {
            headers['Content-Type'] = 'application/x-www-form-urlencoded';
          }
        }
      }

      // Add URL parameters
      const urlObj = new URL(url.includes('://') ? url : `http://${url}`);
      document.querySelectorAll('#params-body tr').forEach(row => {
        const key = row.querySelector('.param-key').value;
        const value = row.querySelector('.param-value').value;
        if (key) {
          urlObj.searchParams.append(key, value);
        }
      });

      // Update URL input with parameters
      let fullUrl = urlObj.toString();

      // Add API key to URL if needed
      if (window.addApiKeyToUrl) {
        fullUrl = window.addApiKeyToUrl(fullUrl);
      }

      requestUrlInput.value = fullUrl;

      try {
        const startTime = Date.now();

        // Get user ID from token or localStorage
        let userId = null;
        const token = localStorage.getItem('token');

        if (token) {
          try {
            // Decode the JWT token to get the user ID
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
              const payload = JSON.parse(atob(tokenParts[1]));
              userId = payload.id;
              console.log('User ID extracted from token for request:', userId);
            }
          } catch (e) {
            console.error('Error extracting user ID from token for request:', e);
          }
        }

        // If we couldn't get the user ID from the token, try localStorage
        if (!userId) {
          try {
            const userData = localStorage.getItem('user');
            if (userData) {
              const user = JSON.parse(userData);
              userId = user.id;
              console.log('User ID from localStorage for request:', userId);
            }
          } catch (e) {
            console.error('Error parsing user data from localStorage for request:', e);
          }
        }

        // If still no user ID, use default
        if (!userId) {
          userId = 1;
          console.warn('No user ID found for request, using default (1)');
        }

        // Prepare request data for our new send endpoint
        const sendRequestData = {
          user_id: userId,
          method,
          url: fullUrl,
          name: requestNameInput.value || fullUrl,
          headers,
          body: bodyType === 'none' ? null : (bodyType === 'form-data' ? body : (body ? body : null)),
          params: {}
        };

        // Extract params from URL
        document.querySelectorAll('#params-body tr').forEach(row => {
          const key = row.querySelector('.param-key').value;
          const value = row.querySelector('.param-value').value;
          if (key) {
            sendRequestData.params[key] = value;
          }
        });

        console.log('Sending request using new send endpoint with data:', JSON.stringify(sendRequestData).substring(0, 200) + '...');

        // Use our new send endpoint
        const sendResponse = await fetch('/api/send/direct', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(sendRequestData)
        });

        // Parse the response from our send endpoint
        const sendResponseData = await sendResponse.json();

        if (!sendResponseData.success) {
          throw new Error(sendResponseData.message || 'Failed to send request');
        }

        console.log('Send endpoint response:', sendResponseData);

        // Extract response data from our send endpoint response
        const responseData = sendResponseData.data.response;
        const responseTime = responseData.response_time || (Date.now() - startTime);

        // Get response status
        const status = responseData.status_code;
        const statusText = status === 200 ? 'OK' : (status >= 400 ? 'Error' : 'Response');

        // Get response headers
        const responseHeaders = responseData.headers || {};

        // Get response body
        let responseBody = responseData.body;

        // Display the response body
        if (typeof responseBody === 'object') {
          // If it's a JSON object, stringify it with pretty formatting
          responseBodyContentEl.textContent = JSON.stringify(responseBody, null, 2);
        } else {
          // Otherwise, display as text
          responseBodyContentEl.textContent = responseBody || '';
        }

        // Update status code
        statusCodeEl.textContent = `${status} ${statusText}`;
        statusCodeEl.className = 'status-code';
        if (status >= 200 && status < 300) {
          statusCodeEl.classList.add('success');
        } else {
          statusCodeEl.classList.add('error');
        }

        // Update response time
        responseTimeEl.textContent = `${responseTime} ms`;

        // Update response headers table
        responseHeadersBodyEl.innerHTML = '';
        Object.entries(responseHeaders).forEach(([key, value]) => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${key}</td>
            <td>${value}</td> `;
        responseHeadersBodyEl.appendChild(row);
      });

      // Save response if there's an active request
      const currentRequestId = saveBtn.getAttribute('data-request-id');
      if (currentRequestId) {
        saveResponse(currentRequestId, {
          status_code: status,
          headers: responseHeaders,
          body: responseBody,
          response_time: responseTime
        });
      }

      // Add request to history
      if (window.addToHistory) {
        // Get the request ID if it exists
        const requestId = saveBtn.getAttribute('data-request-id');
        console.log('Send button - Request ID from saveBtn:', requestId);

        // If the request is not saved yet, save it first
        let finalRequestId = requestId;
        if (!requestId && saveBtn.getAttribute('data-collection-id')) {
          // We have a collection but no request ID, so we should save this request
          console.log('Request not saved yet but has collection, saving first...');

          // We'll still add to history with null request_id for now
          // The user can save the request explicitly if they want to link it
        }

        const requestData = {
          request_id: finalRequestId || null,
          method,
          url: fullUrl,
          name: requestNameInput.value || fullUrl,
          headers,
          params: {},
          body: bodyType === 'none' ? { type: 'none', content: null } : { type: bodyType, content: body },
          auth: window.getAuthorizationData ? window.getAuthorizationData() : null
        };

        // Extract params from URL
        document.querySelectorAll('#params-body tr').forEach(row => {
          const key = row.querySelector('.param-key').value;
          const value = row.querySelector('.param-value').value;
          if (key) {
            requestData.params[key] = value;
          }
        });

        // Create response data for history
        const historyResponseData = {
          status_code: status,
          headers: responseHeaders,
          body: responseBody,
          response_time: responseTime
        };

        // Add to history with response data
        console.log('Preparing to add request to history');
        console.log('Request data before adding user ID:', JSON.stringify(requestData));

        // Get user ID from token first
        let userId = null;
        const token = localStorage.getItem('token');

        if (token) {
          try {
            // Decode the JWT token to get the user ID
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
              const payload = JSON.parse(atob(tokenParts[1]));
              userId = payload.id;
              console.log('User ID extracted from token:', userId);
            }
          } catch (e) {
            console.error('Error extracting user ID from token:', e);
          }
        }

        // If we couldn't get the user ID from the token, try localStorage
        if (!userId) {
          try {
            const userData = localStorage.getItem('user');
            if (userData) {
              const user = JSON.parse(userData);
              userId = user.id;
              console.log('User ID from localStorage:', userId);
            }
          } catch (e) {
            console.error('Error parsing user data from localStorage:', e);
          }
        }

        // Add user ID to request data
        if (userId) {
          requestData.user_id = userId;
          console.log('Added user ID to request data:', userId);
        } else {
          console.warn('No user ID found, using default');
          requestData.user_id = 1; // Default user ID
        }

        console.log('Request data after adding user ID:', JSON.stringify(requestData));

        console.log('Final request data for history:', requestData);

        // Always make a direct API call first to ensure history is saved
        console.log('Making direct API call to add history (primary method)');

        // Ensure requestData has user_id
        if (!requestData.user_id && userId) {
          requestData.user_id = userId;
        }

        // If still no user_id, use default
        if (!requestData.user_id) {
          requestData.user_id = 1;
        }

        console.log('Direct API call - Request data with user_id:', requestData.user_id);

        // Create the history data to send
        const historyApiData = {
          ...requestData,
          user_id: requestData.user_id, // Explicitly include user_id
          status_code: historyResponseData.status_code,
          response_headers: historyResponseData.headers,
          response_body: historyResponseData.body,
          response_time: historyResponseData.response_time
        };

        console.log('Sending history data to API:', JSON.stringify(historyApiData).substring(0, 100) + '...');

        try {
          // Use our new direct endpoint that doesn't require authentication
          console.log('Making API call to direct history endpoint with data:', JSON.stringify(historyApiData).substring(0, 200) + '...');

          // Create a simplified version of the data for the direct endpoint
          const directData = {
            user_id: historyApiData.user_id,
            method: historyApiData.method,
            url: historyApiData.url,
            name: historyApiData.name || historyApiData.url,
            headers: historyApiData.headers,
            body: historyApiData.body,
            params: historyApiData.params,
            status_code: historyApiData.status_code,
            response_headers: historyApiData.response_headers,
            response_body: historyApiData.response_body,
            response_time: historyApiData.response_time
          };

          console.log('Sending direct history data:', JSON.stringify(directData).substring(0, 100) + '...');

          // Call the direct endpoint
          fetch('/api/history/direct', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(directData)
          })
          .then(res => {
            console.log('Direct history endpoint response status:', res.status);
            return res.json();
          })
          .then(data => {
            console.log('Direct history endpoint response:', data);

            if (data.success) {
              console.log('History entry created successfully via direct endpoint!');
            } else {
              throw new Error(data.message || 'Failed to create history entry');
            }
          })
          .catch(err => {
            console.error('Error in direct history endpoint call:', err);

            // Try the original history API as a fallback
            console.log('Trying original history API as fallback');

            fetch('/api/history', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token
              },
              body: JSON.stringify(historyApiData)
            })
            .then(res => {
              console.log('Original history API call response status:', res.status);
              return res.json();
            })
            .then(data => {
              console.log('Original history API call response:', data);
              console.log('History entry created successfully via original API!');
            })
            .catch(fallbackErr => {
              console.error('Error in original history API call:', fallbackErr);

              // Try a minimal data approach as a last resort
              const minimalData = {
                user_id: historyApiData.user_id,
                method: historyApiData.method,
                url: historyApiData.url,
                name: historyApiData.name || historyApiData.url,
                status_code: historyApiData.status_code || 0
              };

              console.log('Trying minimal data as last resort:', minimalData);

              fetch('/api/history/direct', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(minimalData)
              })
              .then(res => {
                console.log('Minimal data history call response status:', res.status);
                return res.json();
              })
              .then(data => {
                console.log('Minimal data history call response:', data);
                console.log('History entry created successfully with minimal data!');
              })
              .catch(minimalErr => {
                console.error('All history API attempts failed:', minimalErr);
              });
            });
          });
        } catch (directError) {
          console.error('Error making direct history API call:', directError);
        }

        // Also try to add to history using the global function as a backup
        if (window.addToHistory) {
          console.log('Adding request to history with response data using global function (backup method)');
          try {
            window.addToHistory(requestData, historyResponseData);
          } catch (historyError) {
            console.error('Error adding to history using global function:', historyError);
          }
        } else {
          console.error('addToHistory function not found');
        }
      }
    } catch (error) {
      console.error(error);
      statusCodeEl.textContent = 'Error';
      statusCodeEl.className = 'status-code error';
      responseTimeEl.textContent = '0 ms';
      responseBodyContentEl.textContent = error.message;
      responseHeadersBodyEl.innerHTML = '';
    }
  });

  // Function to manually set collection or folder ID
  window.setCollectionOrFolderId = function(type, id) {
    if (!saveBtn) {
      console.error('Save button not found in setCollectionOrFolderId');
      return;
    }

    console.log(`Setting ${type} ID on save button:`, id);

    if (type === 'collection') {
      saveBtn.setAttribute('data-collection-id', id);
      saveBtn.removeAttribute('data-folder-id');
    } else if (type === 'folder') {
      saveBtn.setAttribute('data-folder-id', id);
      saveBtn.removeAttribute('data-collection-id');
    }

    // Check the attributes after setting
    window.checkSaveButtonAttributes();
  };

  // Save request button
  console.log('Setting up save button event listener in requests.js');
  if (!saveBtn) {
    console.error('Save button not found in requests.js');
    return;
  }

  // Add event listener without replacing the button
  saveBtn.addEventListener('click', (event) => {
    console.log('Save button clicked in requests.js', event);

    // Visual feedback that the button was clicked
    saveBtn.style.backgroundColor = '#1e7e34';
    setTimeout(() => {
      saveBtn.style.backgroundColor = 'var(--success-color)';
    }, 300);

    // Check save button attributes before proceeding
    const attributes = window.checkSaveButtonAttributes();
    const buttonCollectionId = attributes.collectionId;
    const buttonFolderId = attributes.folderId;

    // If no collection or folder ID is set, show a dialog to select one
    if (!buttonCollectionId && !buttonFolderId) {
      // Create a modal to select a collection or folder
      const selectModal = document.createElement('div');
      selectModal.className = 'modal';
      selectModal.style.display = 'block';
      selectModal.style.position = 'fixed';
      selectModal.style.zIndex = '1000';
      selectModal.style.left = '0';
      selectModal.style.top = '0';
      selectModal.style.width = '100%';
      selectModal.style.height = '100%';
      selectModal.style.overflow = 'auto';
      selectModal.style.backgroundColor = 'rgba(0,0,0,0.4)';

      const modalContent = document.createElement('div');
      modalContent.className = 'modal-content';
      modalContent.style.backgroundColor = '#fefefe';
      modalContent.style.margin = '15% auto';
      modalContent.style.padding = '20px';
      modalContent.style.border = '1px solid #888';
      modalContent.style.width = '50%';
      modalContent.style.borderRadius = '5px';
      modalContent.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

      modalContent.innerHTML = `
        <h2>Select a Collection or Folder</h2>
        <p>Please select a collection or folder to save this request to:</p>
        <div id="select-collections-list" style="max-height: 300px; overflow-y: auto; margin-bottom: 20px;"></div>
        <div style="text-align: right;">
          <button id="select-cancel-btn" class="btn" style="background-color: #dc3545; color: white;">Cancel</button>
        </div>
      `;

      selectModal.appendChild(modalContent);
      document.body.appendChild(selectModal);

      // Load collections into the modal
      const selectCollectionsList = document.getElementById('select-collections-list');
      const collections = document.querySelectorAll('.collection-item');

      collections.forEach(collection => {
        const collectionId = collection.getAttribute('data-id');
        const collectionName = collection.querySelector('.collection-name').textContent;

        const collectionItem = document.createElement('div');
        collectionItem.className = 'select-collection-item';
        collectionItem.style.padding = '10px';
        collectionItem.style.margin = '5px 0';
        collectionItem.style.backgroundColor = '#f8f9fa';
        collectionItem.style.borderRadius = '4px';
        collectionItem.style.cursor = 'pointer';

        collectionItem.innerHTML = `
          <div style="font-weight: bold;">${collectionName}</div>
        `;

        collectionItem.addEventListener('click', () => {
          // Set the collection ID on the save button
          window.setCollectionOrFolderId('collection', collectionId);

          // Close the modal
          document.body.removeChild(selectModal);
        });

        selectCollectionsList.appendChild(collectionItem);
      });

      // Add cancel button event listener
      document.getElementById('select-cancel-btn').addEventListener('click', () => {
        document.body.removeChild(selectModal);
      });

      return;
    }

    const name = requestNameInput.value;
    const method = requestMethodSelect.value;
    const url = requestUrlInput.value;

    if (!name || !url) {
      console.error('Please enter a name and URL');
      return;
    }

    // Get collection ID or folder ID
    const collectionId = saveBtn.getAttribute('data-collection-id');
    const folderId = saveBtn.getAttribute('data-folder-id');

    console.log('Save request - Collection ID:', collectionId, 'Folder ID:', folderId);

    if (!collectionId && !folderId) {
      console.error('Please select a collection or folder');

      // Create a notification element
      const notification = document.createElement('div');
      notification.className = 'notification error';
      notification.textContent = 'Please select a collection or folder before saving';
      notification.style.position = 'fixed';
      notification.style.top = '20px';
      notification.style.left = '50%';
      notification.style.transform = 'translateX(-50%)';
      notification.style.padding = '10px 20px';
      notification.style.backgroundColor = 'var(--danger-color)';
      notification.style.color = 'white';
      notification.style.borderRadius = '4px';
      notification.style.zIndex = '1000';
      notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

      // Add the notification to the body
      document.body.appendChild(notification);

      // Highlight the save button to indicate it needs attention
      saveBtn.style.animation = 'pulse 1s infinite';

      // Remove the notification after 3 seconds
      setTimeout(() => {
        document.body.removeChild(notification);
        saveBtn.style.animation = '';
      }, 3000);

      return;
    }

    // Get headers
    const headers = {};
    document.querySelectorAll('#headers-body tr').forEach(row => {
      const key = row.querySelector('.header-key').value;
      const value = row.querySelector('.header-value').value;
      if (key) {
        headers[key] = value;
      }
    });

    // Get body based on the selected type
    let body = {};
    const bodyType = document.getElementById('body-type').value;

    if (bodyType === 'raw') {
      const rawType = document.getElementById('raw-type').value;
      const rawBody = document.getElementById('raw-body').value;
      body = { type: rawType, content: rawBody };
    } else if (bodyType === 'form-data') {
      const formData = {};
      document.querySelectorAll('#form-data-body tr').forEach(row => {
        const key = row.querySelector('.form-key').value;
        const value = row.querySelector('.form-value').value;
        if (key) {
          formData[key] = value;
        }
      });
      body = { type: 'form-data', content: formData };
    } else if (bodyType === 'x-www-form-urlencoded') {
      const formData = {};
      document.querySelectorAll('#urlencoded-body tr').forEach(row => {
        const key = row.querySelector('.urlencoded-key').value;
        const value = row.querySelector('.urlencoded-value').value;
        if (key) {
          formData[key] = value;
        }
      });
      body = { type: 'x-www-form-urlencoded', content: formData };
    } else {
      body = { type: 'none', content: null };
    }

    // Get URL parameters
    const params = {};
    document.querySelectorAll('#params-body tr').forEach(row => {
      const key = row.querySelector('.param-key').value;
      const value = row.querySelector('.param-value').value;
      if (key) {
        params[key] = value;
      }
    });

    // Get authorization data
    let auth = null;
    if (window.getAuthorizationData) {
      auth = window.getAuthorizationData();
    }

    const requestData = {
      name,
      url,
      method,
      headers,
      body,
      params,
      auth
    };

    // Only add collection_id or folder_id if they exist
    if (collectionId) {
      requestData.collection_id = collectionId;
    }

    if (folderId) {
      requestData.folder_id = folderId;
    }

    console.log('Request data to save:', requestData);

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('You must be logged in to save a request');
      return;
    }

    // Get the request ID from the save button
    const requestId = saveBtn.getAttribute('data-request-id');

    // Check if this is an update (existing request) or a new request
    const isUpdate = !!requestId;

    console.log('Saving request:', isUpdate ? 'UPDATE' : 'CREATE', 'Request ID:', requestId);

    console.log(`Making ${isUpdate ? 'PUT' : 'POST'} request to /api/requests${isUpdate ? `/${requestId}` : ''}`);

    // Show a loading notification
    const loadingNotification = document.createElement('div');
    loadingNotification.className = 'notification loading';
    loadingNotification.textContent = `Saving request...`;
    loadingNotification.style.position = 'fixed';
    loadingNotification.style.top = '20px';
    loadingNotification.style.left = '50%';
    loadingNotification.style.transform = 'translateX(-50%)';
    loadingNotification.style.padding = '10px 20px';
    loadingNotification.style.backgroundColor = '#007bff';
    loadingNotification.style.color = 'white';
    loadingNotification.style.borderRadius = '4px';
    loadingNotification.style.zIndex = '1000';
    loadingNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

    // Add the notification to the body
    document.body.appendChild(loadingNotification);

    fetch(`/api/requests${isUpdate ? `/${requestId}` : ''}`, {
      method: isUpdate ? 'PUT' : 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(requestData)
    })
    .then(res => {
      // Remove the loading notification
      document.body.removeChild(loadingNotification);

      console.log('Save request response status:', res.status);

      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }

      return res.json();
    })
    .then(response => {
      console.log('Save request response:', response);

      // Extract the data from the response
      const data = response.data || response;

      if (data && data.id) {
        // Set request ID for future updates
        saveBtn.setAttribute('data-request-id', data.id);

        // Log success instead of alert
        console.log(`Request ${isUpdate ? 'updated' : 'saved'} successfully`);

        console.log('Request saved successfully. ID:', data.id);
        console.log('Collection ID:', collectionId, 'Folder ID:', folderId);

        // Create a success notification
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = `Request ${isUpdate ? 'updated' : 'saved'} successfully!`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.padding = '10px 20px';
        notification.style.backgroundColor = 'var(--success-color)';
        notification.style.color = 'white';
        notification.style.borderRadius = '4px';
        notification.style.zIndex = '1000';
        notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Add the notification to the body
        document.body.appendChild(notification);

        // Remove the notification after 3 seconds
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 3000);

        // Always add to history when a request is saved or updated
        console.log(`Adding ${isUpdate ? 'updated' : 'newly saved'} request to history with ID:`, data.id);

        // Get user ID from token first
        let userId = null;
        try {
          // Decode the JWT token to get the user ID
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            userId = payload.id;
            console.log('User ID extracted from token for history:', userId);
          }
        } catch (e) {
          console.error('Error extracting user ID from token for history:', e);
        }

        // If we couldn't get the user ID from the token, try localStorage
        if (!userId) {
          try {
            const userData = localStorage.getItem('user');
            if (userData) {
              const user = JSON.parse(userData);
              userId = user.id;
              console.log('User ID from localStorage for history:', userId);
            }
          } catch (e) {
            console.error('Error parsing user data from localStorage for history:', e);
          }
        }

        // Create request data for history
        const historyRequestData = {
          user_id: userId || 1, // Use default if no user ID
          request_id: data.id,
          method: method,
          url: url,
          name: name,
          headers: headers,
          params: params,
          body: body,
          auth: auth
        };

        console.log('History request data for saved request:', historyRequestData);

        // Always make a direct API call first to ensure history is saved
        console.log('Making direct API call to add saved request to history (primary method)');

        try {
          // Use our new direct endpoint that doesn't require authentication
          console.log('Making API call to direct history endpoint for saved request with data:', JSON.stringify(historyRequestData).substring(0, 200) + '...');

          // Create a simplified version of the data for the direct endpoint
          const directData = {
            user_id: historyRequestData.user_id,
            method: historyRequestData.method,
            url: historyRequestData.url,
            name: historyRequestData.name || historyRequestData.url,
            headers: historyRequestData.headers,
            body: historyRequestData.body,
            params: historyRequestData.params,
            request_id: historyRequestData.request_id
          };

          console.log('Sending direct history data for saved request:', JSON.stringify(directData).substring(0, 100) + '...');

          // Call the direct endpoint
          fetch('/api/history/direct', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(directData)
          })
          .then(res => {
            console.log('Direct history endpoint response status for saved request:', res.status);
            return res.json();
          })
          .then(data => {
            console.log('Direct history endpoint response for saved request:', data);

            if (data.success) {
              console.log('History entry for saved request created successfully via direct endpoint!');
            } else {
              throw new Error(data.message || 'Failed to create history entry for saved request');
            }
          })
          .catch(err => {
            console.error('Error in direct history endpoint call for saved request:', err);

            // Try the original history API as a fallback
            console.log('Trying original history API as fallback for saved request');

            fetch('/api/history', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token
              },
              body: JSON.stringify(historyRequestData)
            })
            .then(res => {
              console.log('Original history API call response status for saved request:', res.status);
              return res.json();
            })
            .then(data => {
              console.log('Original history API call response for saved request:', data);
              console.log('History entry for saved request created successfully via original API!');
            })
            .catch(fallbackErr => {
              console.error('Error in original history API call for saved request:', fallbackErr);

              // Try a minimal data approach as a last resort
              const minimalData = {
                user_id: historyRequestData.user_id,
                method: historyRequestData.method,
                url: historyRequestData.url,
                name: historyRequestData.name || historyRequestData.url,
                request_id: historyRequestData.request_id
              };

              console.log('Trying minimal data as last resort for saved request:', minimalData);

              fetch('/api/history/direct', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(minimalData)
              })
              .then(res => {
                console.log('Minimal data history call response status for saved request:', res.status);
                return res.json();
              })
              .then(data => {
                console.log('Minimal data history call response for saved request:', data);
                console.log('History entry for saved request created successfully with minimal data!');
              })
              .catch(minimalErr => {
                console.error('All history API attempts failed for saved request:', minimalErr);
              });
            });
          });
        } catch (directError) {
          console.error('Error making direct history API call for saved request:', directError);
        }

        // Also try to add to history using the global function as a backup
        if (window.addToHistory) {
          console.log('Adding saved request to history using global function (backup method)');
          try {
            window.addToHistory(historyRequestData, null);
          } catch (historyError) {
            console.error('Error adding to history using global function:', historyError);
          }
        } else {
          console.error('addToHistory function not found for saved request');
        }

        // Reload collection or folder with improved refresh logic
        if (collectionId) {
          console.log('Reloading collection:', collectionId);
          if (window.loadCollection) {
            window.loadCollection(collectionId);

            // Find and expand the collection to show its contents
            setTimeout(() => {
              const collectionEl = document.querySelector(`.collection-item[data-id="${collectionId}"]`);
              if (collectionEl && !collectionEl.classList.contains('active')) {
                collectionEl.querySelector('.collection-header').click();
              }
            }, 300);
          } else {
            console.error('loadCollection function not found');
          }
        } else if (folderId) {
          console.log('Reloading folder:', folderId);

          // First, ensure the folder is expanded
          const folderEl = document.querySelector(`.folder-item[data-id="${folderId}"]`);
          if (folderEl) {
            // Make sure the folder is expanded
            if (!folderEl.classList.contains('expanded')) {
              folderEl.classList.add('expanded');
            }

            // Force reload the folder content
            if (window.loadFolder) {
              window.loadFolder(folderId);
            } else {
              console.error('loadFolder function not found');

              // Try to find the folder element and reload it manually
              console.log('Found folder element, triggering click');
              const folderHeader = folderEl.querySelector('.folder-header');
              if (folderHeader) {
                // Click twice to ensure it expands (if collapsed) and refreshes
                folderHeader.click();
                setTimeout(() => folderHeader.click(), 100);
              }
            }
          } else {
            console.error('Folder element not found for ID:', folderId);

            // Try to find the parent folder or collection and reload it
            const collections = document.querySelectorAll('.collection-item');
            collections.forEach(collection => {
              if (window.loadCollection) {
                window.loadCollection(collection.getAttribute('data-id'));
              }
            });
          }
        }
      } else {
        const errorMessage = response.message || (data ? data.message : 'Unknown error');
        console.error('Request saving failed:', errorMessage);

        // Create an error notification
        const errorNotification = document.createElement('div');
        errorNotification.className = 'notification error';
        errorNotification.textContent = `Error saving request: ${errorMessage}`;
        errorNotification.style.position = 'fixed';
        errorNotification.style.top = '20px';
        errorNotification.style.left = '50%';
        errorNotification.style.transform = 'translateX(-50%)';
        errorNotification.style.padding = '10px 20px';
        errorNotification.style.backgroundColor = 'var(--danger-color)';
        errorNotification.style.color = 'white';
        errorNotification.style.borderRadius = '4px';
        errorNotification.style.zIndex = '1000';
        errorNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Add the notification to the body
        document.body.appendChild(errorNotification);

        // Remove the notification after 5 seconds
        setTimeout(() => {
          document.body.removeChild(errorNotification);
        }, 5000);
      }
    })
    .catch(err => {
      console.error('Error saving request:', err);

      // Create an error notification
      const errorNotification = document.createElement('div');
      errorNotification.className = 'notification error';
      errorNotification.textContent = `Error saving request: ${err.message || 'Unknown error'}`;
      errorNotification.style.position = 'fixed';
      errorNotification.style.top = '20px';
      errorNotification.style.left = '50%';
      errorNotification.style.transform = 'translateX(-50%)';
      errorNotification.style.padding = '10px 20px';
      errorNotification.style.backgroundColor = 'var(--danger-color)';
      errorNotification.style.color = 'white';
      errorNotification.style.borderRadius = '4px';
      errorNotification.style.zIndex = '1000';
      errorNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

      // Add the notification to the body
      document.body.appendChild(errorNotification);

      // Remove the notification after 5 seconds
      setTimeout(() => {
        document.body.removeChild(errorNotification);
      }, 5000);

      console.error('Request saving failed. Please try again.');
    });
  });

  // Save response function
  function saveResponse(requestId, responseData) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/requests/${requestId}/responses`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(responseData)
    })
    .then(res => res.json())
    .catch(err => {
      console.error('Failed to save response', err);
    });
  }

  // Clear request form
  window.clearRequestForm = function() {
    console.log('Clearing request form');

    // Clear form fields
    requestNameInput.value = '';
    requestUrlInput.value = '';
    requestMethodSelect.value = 'GET';

    // Clear headers
    const headersBody = document.getElementById('headers-body');
    headersBody.innerHTML = '';

    // Add empty header row
    const headerRow = document.createElement('tr');
    headerRow.innerHTML = `
      <td><input type="text" class="header-key"></td>
      <td><input type="text" class="header-value"></td>
      <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
    `;

    // Remove header event
    headerRow.querySelector('.remove-header').addEventListener('click', () => {
      headersBody.removeChild(headerRow);
    });

    headersBody.appendChild(headerRow);

    // Clear params
    const paramsBody = document.getElementById('params-body');
    paramsBody.innerHTML = '';

    // Add empty param row
    const paramRow = document.createElement('tr');
    paramRow.innerHTML = `
      <td><input type="text" class="param-key"></td>
      <td><input type="text" class="param-value"></td>
      <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
    `;

    // Remove param event
    paramRow.querySelector('.remove-param').addEventListener('click', () => {
      paramsBody.removeChild(paramRow);
    });

    paramsBody.appendChild(paramRow);

    // Reset body type
    document.getElementById('body-type').value = 'none';

    // Trigger change event to show the correct body container
    const event = new Event('change');
    document.getElementById('body-type').dispatchEvent(event);

    // Clear raw body
    document.getElementById('raw-body').value = '';
    document.getElementById('raw-type').value = 'text';

    // Clear form data
    const formDataBody = document.getElementById('form-data-body');
    formDataBody.innerHTML = '';

    // Add empty form data row
    const formDataRow = document.createElement('tr');
    formDataRow.innerHTML = `
      <td><input type="text" class="form-key"></td>
      <td><input type="text" class="form-value"></td>
      <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
    `;

    // Remove form data event
    formDataRow.querySelector('.remove-form').addEventListener('click', () => {
      formDataBody.removeChild(formDataRow);
    });

    formDataBody.appendChild(formDataRow);

    // Clear URL encoded
    const urlencodedBody = document.getElementById('urlencoded-body');
    urlencodedBody.innerHTML = '';

    // Add empty URL encoded row
    const urlencodedRow = document.createElement('tr');
    urlencodedRow.innerHTML = `
      <td><input type="text" class="urlencoded-key"></td>
      <td><input type="text" class="urlencoded-value"></td>
      <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
    `;

    // Remove URL encoded event
    urlencodedRow.querySelector('.remove-urlencoded').addEventListener('click', () => {
      urlencodedBody.removeChild(urlencodedRow);
    });

    urlencodedBody.appendChild(urlencodedRow);

    // Clear response area
    const responseHeadersBodyEl = document.getElementById('response-headers-body');
    const responseBodyContentEl = document.getElementById('response-body-content');
    const statusCodeEl = document.getElementById('status-code');
    const responseTimeEl = document.getElementById('response-time');

    if (responseHeadersBodyEl) responseHeadersBodyEl.innerHTML = '';
    if (responseBodyContentEl) responseBodyContentEl.textContent = '';
    if (statusCodeEl) {
      statusCodeEl.textContent = '';
      statusCodeEl.className = 'status-code';
    }
    if (responseTimeEl) responseTimeEl.textContent = '';

    // Clear request ID from buttons
    const sendBtn = document.getElementById('send-btn');
    const saveBtn = document.getElementById('save-btn');

    if (sendBtn) sendBtn.removeAttribute('data-request-id');
    if (saveBtn) {
      saveBtn.removeAttribute('data-request-id');
      saveBtn.removeAttribute('data-collection-id');
      saveBtn.removeAttribute('data-folder-id');
    }

    // Remove view history button if it exists
    const viewHistoryBtn = document.getElementById('view-history-btn');
    if (viewHistoryBtn && viewHistoryBtn.parentNode) {
      viewHistoryBtn.parentNode.removeChild(viewHistoryBtn);
    }
  };

  // Add request to folder
  window.addRequestToFolder = function(folderId) {
    console.log('Adding request to folder:', folderId);

    // Use the clearRequestForm function to reset the form
    if (window.clearRequestForm) {
      window.clearRequestForm();
    } else {
      // Fallback if clearRequestForm is not available
      requestNameInput.value = '';
      requestUrlInput.value = '';
      requestMethodSelect.value = 'GET';

      // Clear headers, params, and body
      document.getElementById('headers-body').innerHTML = '';
      document.getElementById('params-body').innerHTML = '';
      document.getElementById('raw-body').value = '';
    }

    // Set folder ID for the save button
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      // Set folder ID and remove collection ID
      saveBtn.setAttribute('data-folder-id', folderId);
      saveBtn.removeAttribute('data-collection-id');
      saveBtn.removeAttribute('data-request-id');
    }

    // Show requests container and hide auth container
    const authContainer = document.getElementById('auth-container');
    const requestsContainer = document.getElementById('requests-container');

    if (authContainer) {
      authContainer.classList.add('hidden');
    }

    if (requestsContainer) {
      requestsContainer.classList.remove('hidden');
    }

    // Activate params tab by default
    const paramsTabBtn = document.querySelector('.tab-btn[data-tab="params"]');
    const paramsTab = document.getElementById('params-tab');

    if (paramsTabBtn) {
      document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
      paramsTabBtn.classList.add('active');
    }

    if (paramsTab) {
      document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
      paramsTab.classList.add('active');
    }
  };

  // Load requests function
  window.loadRequests = function(collectionId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/collections/${collectionId}`, {
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => res.json())
    .then(data => {
      if (data.requests) {
        // Create requests list
        const requestsList = document.createElement('div');
        requestsList.className = 'requests-list';

        data.requests.forEach(request => {
          const requestItem = document.createElement('div');
          requestItem.className = 'request-item';
          requestItem.innerHTML = `
            <div class="request-item-method">${request.method}</div>
            <div class="request-item-name">${request.name}</div>
          `;

          // Click event to load request
          requestItem.addEventListener('click', () => {
            loadRequest(request.id);
          });

          requestsList.appendChild(requestItem);
        });

        // Add to collections list under the active collection
        const activeCollection = document.querySelector('.collection-item.active');

        // Remove existing requests list
        const existingList = activeCollection.querySelector('.requests-list');
        if (existingList) {
          activeCollection.removeChild(existingList);
        }

        activeCollection.appendChild(requestsList);
      }
    })
    .catch(err => {
      console.error(err);
    });
  };

  // Load request function
  window.loadRequest = function(requestId) {
    console.log('Loading request:', requestId);
    const token = localStorage.getItem('token');
    if (!token) return;

    // Show loading state in request form
    const requestNameInput = document.getElementById('request-name');
    const requestUrlInput = document.getElementById('request-url');

    if (requestNameInput) requestNameInput.value = 'Loading...';
    if (requestUrlInput) requestUrlInput.value = 'Loading...';

    // Show requests container and hide auth container
    const authContainer = document.getElementById('auth-container');
    const requestsContainer = document.getElementById('requests-container');

    if (authContainer) authContainer.classList.add('hidden');
    if (requestsContainer) requestsContainer.classList.remove('hidden');

    fetch(`/api/requests/${requestId}`, {
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      if (!res.ok) {
        throw new Error(`Failed to fetch request: ${res.status}`);
      }
      return res.json();
    })
    .then(response => {
      console.log('Request API response:', response);

      // Extract the request data from the response
      const request = response.data || response;

      console.log('Request data:', request);

      if (!request || !request.id) {
        console.error('Invalid request data:', request);
        throw new Error('Invalid request data received');
      }

      // Set request ID for save button
      const saveBtn = document.getElementById('save-btn');
      if (saveBtn) {
        saveBtn.setAttribute('data-request-id', request.id);

        // Set collection_id or folder_id
        if (request.collection_id) {
          console.log('Setting collection_id:', request.collection_id);
          saveBtn.setAttribute('data-collection-id', request.collection_id);
          saveBtn.removeAttribute('data-folder-id');
        } else if (request.folder_id) {
          console.log('Setting folder_id:', request.folder_id);
          saveBtn.setAttribute('data-folder-id', request.folder_id);
          saveBtn.removeAttribute('data-collection-id');
        }
      } else {
        console.error('Save button not found');
      }

      // Fill request form
      requestNameInput.value = request.name;
      requestMethodSelect.value = request.method;
      requestUrlInput.value = request.url;

      // Add View History button if it doesn't exist
      let viewHistoryBtn = document.getElementById('view-history-btn');
      if (!viewHistoryBtn) {
        viewHistoryBtn = document.createElement('button');
        viewHistoryBtn.id = 'view-history-btn';
        viewHistoryBtn.className = 'btn';
        viewHistoryBtn.innerHTML = '<i class="fas fa-history"></i> View History';

        // Insert after save button
        const saveBtn = document.getElementById('save-btn');
        if (saveBtn && saveBtn.parentNode) {
          saveBtn.parentNode.insertBefore(viewHistoryBtn, saveBtn.nextSibling);
        }
      }

      // Set request ID for history button
      viewHistoryBtn.setAttribute('data-request-id', request.id);

      // Add click event to view history
      viewHistoryBtn.addEventListener('click', () => {
        const requestId = viewHistoryBtn.getAttribute('data-request-id');
        if (requestId && window.loadRequestHistory) {
          window.loadRequestHistory(requestId);
        }
      });

      // Load authorization data
      if (window.setAuthorizationData && request.auth) {
        window.setAuthorizationData(request.auth);
      }

      // Fill headers
      const headersBody = document.getElementById('headers-body');
      headersBody.innerHTML = '';

      if (request.headers && Object.keys(request.headers).length > 0) {
        Object.entries(request.headers).forEach(([key, value]) => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td><input type="text" class="header-key" value="${key}"></td>
            <td><input type="text" class="header-value" value="${value}"></td>
            <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
          `;

          // Remove header event
          row.querySelector('.remove-header').addEventListener('click', () => {
            headersBody.removeChild(row);
          });

          headersBody.appendChild(row);
        });
      } else {
        // Add empty row
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="text" class="header-key"></td>
          <td><input type="text" class="header-value"></td>
          <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
        `;

        // Remove header event
        row.querySelector('.remove-header').addEventListener('click', () => {
          headersBody.removeChild(row);
        });

        headersBody.appendChild(row);
      }

      // Fill params
      const paramsBody = document.getElementById('params-body');
      paramsBody.innerHTML = '';

      if (request.params && Object.keys(request.params).length > 0) {
        Object.entries(request.params).forEach(([key, value]) => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td><input type="text" class="param-key" value="${key}"></td>
            <td><input type="text" class="param-value" value="${value}"></td>
            <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
          `;

          // Remove param event
          row.querySelector('.remove-param').addEventListener('click', () => {
            paramsBody.removeChild(row);
          });

          paramsBody.appendChild(row);
        });
      } else {
        // Add empty row
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="text" class="param-key"></td>
          <td><input type="text" class="param-value"></td>
          <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
        `;

        // Remove param event
        row.querySelector('.remove-param').addEventListener('click', () => {
          paramsBody.removeChild(row);
        });

        paramsBody.appendChild(row);
      }

      // Set body type
      if (request.body && request.body.type) {
        document.getElementById('body-type').value = request.body.type;

        // Trigger change event to show the correct body container
        const event = new Event('change');
        document.getElementById('body-type').dispatchEvent(event);

        // Fill body content based on type
        if (request.body.type === 'raw') {
          document.getElementById('raw-type').value = request.body.rawType || 'text';
          document.getElementById('raw-body').value = request.body.content || '';
        } else if (request.body.type === 'form-data') {
          const formDataBody = document.getElementById('form-data-body');
          formDataBody.innerHTML = '';

          if (request.body.content && Object.keys(request.body.content).length > 0) {
            Object.entries(request.body.content).forEach(([key, value]) => {
              const row = document.createElement('tr');
              row.innerHTML = `
                <td><input type="text" class="form-key" value="${key}"></td>
                <td><input type="text" class="form-value" value="${value}"></td>
                <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
              `;

              // Remove form field event
              row.querySelector('.remove-form').addEventListener('click', () => {
                formDataBody.removeChild(row);
              });

              formDataBody.appendChild(row);
            });
          } else {
            // Add empty row
            const row = document.createElement('tr');
            row.innerHTML = `
              <td><input type="text" class="form-key"></td>
              <td><input type="text" class="form-value"></td>
              <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
            `;

            // Remove form field event
            row.querySelector('.remove-form').addEventListener('click', () => {
              formDataBody.removeChild(row);
            });

            formDataBody.appendChild(row);
          }
        } else if (request.body.type === 'x-www-form-urlencoded') {
          const urlencodedBody = document.getElementById('urlencoded-body');
          urlencodedBody.innerHTML = '';

          if (request.body.content && Object.keys(request.body.content).length > 0) {
            Object.entries(request.body.content).forEach(([key, value]) => {
              const row = document.createElement('tr');
              row.innerHTML = `
                <td><input type="text" class="urlencoded-key" value="${key}"></td>
                <td><input type="text" class="urlencoded-value" value="${value}"></td>
                <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
              `;

              // Remove urlencoded field event
              row.querySelector('.remove-urlencoded').addEventListener('click', () => {
                urlencodedBody.removeChild(row);
              });

              urlencodedBody.appendChild(row);
            });
          } else {
            // Add empty row
            const row = document.createElement('tr');
            row.innerHTML = `
              <td><input type="text" class="urlencoded-key"></td>
              <td><input type="text" class="urlencoded-value"></td>
              <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
            `;

            // Remove urlencoded field event
            row.querySelector('.remove-urlencoded').addEventListener('click', () => {
              urlencodedBody.removeChild(row);
            });

            urlencodedBody.appendChild(row);
          }
        }
      }

      // If there's a response, display it
      if (request.responses && request.responses.length > 0) {
        const latestResponse = request.responses[0];

        // Update status code
        statusCodeEl.textContent = `${latestResponse.status_code}`;
        statusCodeEl.className = 'status-code';
        if (latestResponse.status_code >= 200 && latestResponse.status_code < 300) {
          statusCodeEl.classList.add('success');
        } else {
          statusCodeEl.classList.add('error');
        }

        // Update response time
        responseTimeEl.textContent = `${latestResponse.response_time} ms`;

        // Update response body
        if (typeof latestResponse.body === 'object') {
          responseBodyContentEl.textContent = JSON.stringify(latestResponse.body, null, 2);
        } else {
          responseBodyContentEl.textContent = latestResponse.body;
        }

        // Update response headers
        responseHeadersBodyEl.innerHTML = '';
        Object.entries(latestResponse.headers).forEach(([key, value]) => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${key}</td>
            <td>${value}</td>
          `;
          responseHeadersBodyEl.appendChild(row);
        });
      } else {
        // Clear response
        statusCodeEl.textContent = '';
        responseTimeEl.textContent = '';
        responseBodyContentEl.textContent = '';
        responseHeadersBodyEl.innerHTML = '';
      }
    })
    .catch(err => {
      console.error('Error loading request:', err);

      // Show error message
      const requestNameInput = document.getElementById('request-name');
      const requestUrlInput = document.getElementById('request-url');

      if (requestNameInput) requestNameInput.value = '';
      if (requestUrlInput) requestUrlInput.value = '';

      // Create an error notification
      const errorNotification = document.createElement('div');
      errorNotification.className = 'notification error';
      errorNotification.textContent = `Error loading request: ${err.message || 'Unknown error'}`;
      errorNotification.style.position = 'fixed';
      errorNotification.style.top = '20px';
      errorNotification.style.left = '50%';
      errorNotification.style.transform = 'translateX(-50%)';
      errorNotification.style.padding = '10px 20px';
      errorNotification.style.backgroundColor = 'var(--danger-color)';
      errorNotification.style.color = 'white';
      errorNotification.style.borderRadius = '4px';
      errorNotification.style.zIndex = '1000';
      errorNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

      // Add the notification to the body
      document.body.appendChild(errorNotification);

      // Remove the notification after 5 seconds
      setTimeout(() => {
        document.body.removeChild(errorNotification);
      }, 5000);
    });
  }
});