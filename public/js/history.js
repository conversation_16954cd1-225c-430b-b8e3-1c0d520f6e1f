document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const historyList = document.getElementById('history-list');
  const clearHistoryBtn = document.getElementById('clear-history-btn');
  const sidebarTabs = document.querySelectorAll('.sidebar-tab');

  // Constants
  const API_URL = '/api';

  // Utility function to safely parse JSON
  function safeJSONParse(str, defaultValue = {}) {
    try {
      return str ? JSON.parse(str) : defaultValue;
    } catch (error) {
      console.error('JSON parsing error:', error);
      return defaultValue;
    }
  }

  // Get user ID from token
  function getUserId() {
    const token = localStorage.getItem('token');
    if (!token) return null;

    try {
      // JWT tokens are in the format: header.payload.signature
      // We need the payload part
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      // Decode the payload (base64)
      const payload = JSON.parse(atob(parts[1]));
      return payload.id || null;
    } catch (error) {
      console.error('Error parsing token:', error);
      return null;
    }
  }

  // Generic error handler
  function handleError(context, error) {
    console.error(`Error in ${context}:`, error);
    return error.message || 'An unknown error occurred';
  }

  // Show error message in UI
  function showErrorMessage(message, container = historyList) {
    if (!container) return;

    container.innerHTML = '';
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.textContent = message;
    container.appendChild(errorMessage);
  }

  // Function to clear all history
  function clearAllHistory() {
    const token = localStorage.getItem('token');
    if (!token) {
      showErrorMessage('You must be logged in to clear history');
      return;
    }

    if (historyList) {
      historyList.innerHTML = '';
      const loadingMessage = document.createElement('div');
      loadingMessage.className = 'loading-message';
      loadingMessage.textContent = 'Clearing history...';
      historyList.appendChild(loadingMessage);
    }

    fetch(`${API_URL}/history`, {
      method: 'DELETE',
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      if (!res.ok) throw new Error(`Failed to clear history: ${res.status}`);
      return res.json();
    })
    .then(response => {
      console.log('History cleared:', response);

      if (historyList) {
        historyList.innerHTML = '';
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-history-message';
        emptyMessage.textContent = 'No request history yet';
        historyList.appendChild(emptyMessage);
      }
    })
    .catch(err => {
      handleError('clearAllHistory', err);
      renderHistory();
    });
  }

  // Render history items
  function renderHistory() {
    // Load the first page of history with default limit
    loadHistoryPage(1, 50);
  }

  // Function to load a specific page of history
  function loadHistoryPage(page, limit) {
    if (!historyList) {
      console.error('History list element not found');
      return;
    }

    historyList.innerHTML = '';
    const loadingMessage = document.createElement('div');
    loadingMessage.className = 'loading-message';
    loadingMessage.textContent = 'Loading history...';
    historyList.appendChild(loadingMessage);

    const token = localStorage.getItem('token');
    if (!token) {
      showErrorMessage('You must be logged in to view history');
      return;
    }

    // Get user ID from token or localStorage
    const userId = getUserId();

    // Always use 123 as the workspace ID for history since that's where the data is stored
    const workspaceId = 123;

    // Prepare request data for the filter endpoint
    const requestData = {
      user_id: userId,
      workspace_id: workspaceId,
      limit: limit || 50,
      page: page || 1
    };

    console.log('Loading history page:', requestData);

    // Use the POST endpoint for filtering history
    fetch(`${API_URL}/history/filter`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(requestData)
    })
    .then(res => {
      if (!res.ok) throw new Error(`History API returned status: ${res.status}`);
      return res.json();
    })
    .then(response => {
      historyList.innerHTML = '';
      console.log('History API response for page', page, ':', response);

      // Extract history data and pagination info
      let historyItems = [];
      let pagination = { total: 0, page: page, limit: limit, totalPages: 1 };

      if (response && response.status === 'Success' && response.data) {
        console.log('Processing successful response with data:', response.data);

        if (response.data.items) {
          // New format with pagination
          historyItems = response.data.items;
          pagination = response.data.pagination || pagination;
          console.log('Extracted items:', historyItems.length, 'and pagination:', pagination);
        } else if (Array.isArray(response.data)) {
          // Old format without pagination
          historyItems = response.data;
          console.log('Using array data directly:', historyItems.length);
        } else {
          // Unknown format
          console.warn('Unknown response format:', response);
          historyItems = [];
        }
      } else if (Array.isArray(response)) {
        // Direct array response (old format)
        historyItems = response;
        console.log('Using direct array response:', historyItems.length);
      } else {
        console.error('Unexpected response format:', response);
      }

      if (!historyItems || historyItems.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-history-message';
        emptyMessage.textContent = 'No request history yet';
        historyList.appendChild(emptyMessage);
        return;
      }

      // Add pagination controls if we have multiple pages
      if (pagination.totalPages > 1) {
        addPaginationControls(pagination, historyList);
      }

      historyItems.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.setAttribute('data-id', item.id || item.api_request_history_Id);

        // Format the date
        const date = new Date(item.created_at);
        const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

        // Determine method
        const method = item.method || item.api_request_Method || 'GET';
        const methodClass = method.toLowerCase();

        // Determine URL
        const url = item.url || item.api_request_Url || 'Unknown URL';

        // Status code handling
        const statusCode = item.status_code || item.api_request_history_Status_Code;
        const statusText = statusCode ?
          `<div class="history-item-status ${statusCode >= 200 && statusCode < 300 ? 'success' : 'error'}">${statusCode}</div>`
          : '';

        historyItem.innerHTML = `
          <div class="history-item-method ${methodClass}">${method}</div>
          ${statusText}
          <div class="history-item-details">
            <div class="history-item-url">${url}</div>
            <div class="history-item-time">${formattedDate}</div>
          </div>
          <div class="history-item-actions">
            <button class="btn-icon delete-history-btn" title="Delete this history item">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        `;

        // Delete button event listener
        const deleteBtn = historyItem.querySelector('.delete-history-btn');
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          deleteHistoryItem(item.id || item.api_request_history_Id);
        });

        // Click event to load request from history
        historyItem.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();

          // Switch to the requests tab
          const requestsTab = document.querySelector('.sidebar-tab[data-tab="requests"]');
          if (requestsTab) {
            requestsTab.click();
          }

          // Load the request
          loadHistoryRequest(item);
        });

        historyList.appendChild(historyItem);
      });
    })
    .catch(err => {
      console.error('Error fetching history page:', err);
      handleError('loadHistoryPage', err);
      showErrorMessage(`Failed to load history: ${err.message}`);
    });
  }

  // Function to add pagination controls
  function addPaginationControls(pagination, container) {
    const { page, totalPages, total, limit } = pagination;

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-controls';

    // Add pagination info
    const paginationInfo = document.createElement('div');
    paginationInfo.className = 'pagination-info';
    paginationInfo.textContent = `Showing ${(page - 1) * limit + 1}-${Math.min(page * limit, total)} of ${total} items`;
    paginationContainer.appendChild(paginationInfo);

    // Add pagination buttons
    const paginationButtons = document.createElement('div');
    paginationButtons.className = 'pagination-buttons';

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.className = 'pagination-button';
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.disabled = page <= 1;
    prevButton.addEventListener('click', () => {
      if (page > 1) {
        loadHistoryPage(page - 1, limit);
      }
    });
    paginationButtons.appendChild(prevButton);

    // Page number buttons
    const maxButtons = 5; // Maximum number of page buttons to show
    const startPage = Math.max(1, page - Math.floor(maxButtons / 2));
    const endPage = Math.min(totalPages, startPage + maxButtons - 1);

    for (let i = startPage; i <= endPage; i++) {
      const pageButton = document.createElement('button');
      pageButton.className = 'pagination-button';
      if (i === page) {
        pageButton.classList.add('active');
      }
      pageButton.textContent = i;
      pageButton.addEventListener('click', () => {
        if (i !== page) {
          loadHistoryPage(i, limit);
        }
      });
      paginationButtons.appendChild(pageButton);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.className = 'pagination-button';
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.disabled = page >= totalPages;
    nextButton.addEventListener('click', () => {
      if (page < totalPages) {
        loadHistoryPage(page + 1, limit);
      }
    });
    paginationButtons.appendChild(nextButton);

    paginationContainer.appendChild(paginationButtons);

    // Add pagination controls to the container
    container.appendChild(paginationContainer);
  }

  // Initial render of history
  renderHistory();
});
  function deleteHistoryItem(historyId) {
    const token = localStorage.getItem('token');
    if (!token) {
      showErrorMessage('You must be logged in to delete history items');
      return;
    }

    const historyItem = document.querySelector(`.history-item[data-id="${historyId}"]`);
    if (historyItem) {
      historyItem.classList.add('deleting');
      const deleteBtn = historyItem.querySelector('.delete-history-btn');
      if (deleteBtn) {
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;
      }
    }

    fetch(`${API_URL}/history/${historyId}`, {
      method: 'DELETE',
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      if (!res.ok) throw new Error(`Failed to delete history item: ${res.status}`);
      return res.json();
    })
    .then(response => {
      console.log('History item deleted:', response);

      if (historyItem) {
        historyItem.style.height = `${historyItem.offsetHeight}px`;
        historyItem.classList.add('deleted');

        setTimeout(() => {
          historyItem.remove();

          // Check if no history items left
          const remainingItems = document.querySelectorAll('.history-item');
          if (remainingItems.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-history-message';
            emptyMessage.textContent = 'No request history yet';
            historyList.appendChild(emptyMessage);
          }
        }, 300);
      } else {
        renderHistory();
      }
    })
    .catch(err => {
      handleError('deleteHistoryItem', err);

      // Reset delete button
      if (historyItem) {
        historyItem.classList.remove('deleting');
        const deleteBtn = historyItem.querySelector('.delete-history-btn');
        if (deleteBtn) {
          deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
          deleteBtn.disabled = false;
        }
      }
    });
  }

  // Load request from history
  function loadHistoryRequest(historyItem) {
    const token = localStorage.getItem('token');
    if (!token) return;

    console.log('Loading history item:', historyItem);

    // Get history ID
    const historyId = historyItem.id || historyItem.api_request_history_Id;
    if (!historyId) {
      console.error('History item has no ID');
      return;
    }

    // Show loading state in request form
    const requestNameEl = document.getElementById('request-name');
    const requestUrlEl = document.getElementById('request-url');
    const requestMethodEl = document.getElementById('request-method');

    if (requestNameEl) requestNameEl.value = 'Loading...';
    if (requestUrlEl) requestUrlEl.value = 'Loading...';

    // Show requests container and hide auth container
    const authContainer = document.getElementById('auth-container');
    const requestsContainer = document.getElementById('requests-container');

    if (authContainer) authContainer.classList.add('hidden');
    if (requestsContainer) requestsContainer.classList.remove('hidden');

    // First try to extract data directly from the history item
    let method = 'GET';
    let url = '';
    let name = '';
    let headers = {};
    let params = {};
    let body = {};
    let responseData = null;

    // Try to extract data from api_request_history_Response if it exists
    if (historyItem.api_request_history_Response) {
      try {
        // Check if it's already an object
        if (typeof historyItem.api_request_history_Response === 'object') {
          responseData = historyItem.api_request_history_Response;
        } else {
          responseData = safeJSONParse(historyItem.api_request_history_Response);
        }

        console.log('Extracted response data:', responseData);

        if (responseData && responseData.request) {
          method = responseData.request.method || method;
          url = responseData.request.url || url;
          headers = responseData.request.headers || headers;
          params = responseData.request.params || params;
          body = responseData.request.body || body;
        }
      } catch (e) {
        console.error('Error parsing response data:', e);
      }
    }

    // If we couldn't extract from response data, try other fields
    method = historyItem.api_request_Method || historyItem.method || method;
    url = historyItem.api_request_Url || historyItem.url || url;
    name = historyItem.api_request_Name || historyItem.name || url;

    // Set the basic request details
    if (requestNameEl) requestNameEl.value = name;
    if (requestUrlEl) requestUrlEl.value = url;
    if (requestMethodEl) requestMethodEl.value = method;

    // If we have enough data, we can populate the form without making another request
    if (url) {
      // Populate headers
      populateHeaders(headers);

      // Populate params
      populateParams(params);

      // Populate body
      populateBody(body);

      // If we have response data, display it
      if (responseData && responseData.response) {
        displayResponse(responseData.response);
      }

      console.log('Request loaded from history item data');
      return;
    }

    // If we don't have enough data, fetch the full history item
    console.log('Fetching full history item data...');
    fetch(`${API_URL}/history/${historyId}`, {
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      if (!res.ok) throw new Error(`Failed to fetch history item: ${res.status}`);
      return res.json();
    })
    .then(response => {
      console.log('Fetched history item:', response);

      // Extract the actual history item data
      const historyData = response.data || response;

      // Extract method, url, and name
      method = historyData.api_request_Method || historyData.method || method;
      url = historyData.api_request_Url || historyData.url || url;
      name = historyData.api_request_Name || historyData.name || url;

      // Set the basic request details
      if (requestNameEl) requestNameEl.value = name;
      if (requestUrlEl) requestUrlEl.value = url;
      if (requestMethodEl) requestMethodEl.value = method;

      // Try to extract response data
      if (historyData.api_request_history_Response) {
        try {
          // Check if it's already an object
          if (typeof historyData.api_request_history_Response === 'object') {
            responseData = historyData.api_request_history_Response;
          } else {
            responseData = safeJSONParse(historyData.api_request_history_Response);
          }

          console.log('Extracted response data from API:', responseData);

          if (responseData && responseData.request) {
            headers = responseData.request.headers || headers;
            params = responseData.request.params || params;
            body = responseData.request.body || body;
          }

          // Populate headers, params, and body
          populateHeaders(headers);
          populateParams(params);
          populateBody(body);

          // If we have response data, display it
          if (responseData && responseData.response) {
            displayResponse(responseData.response);
          }
        } catch (e) {
          console.error('Error parsing response data from API:', e);
        }
      }
    })
    .catch(err => {
      handleError('loadHistoryRequest', err);
      showErrorMessage('Failed to load history item');
    });
  }

  // Helper function to populate headers
  function populateHeaders(headers) {
    const headersBody = document.getElementById('headers-body');
    if (!headersBody) return;

    headersBody.innerHTML = '';

    if (headers && Object.keys(headers).length > 0) {
      Object.entries(headers).forEach(([key, value]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="text" class="header-key" value="${key}"></td>
          <td><input type="text" class="header-value" value="${value}"></td>
          <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
        `;

        // Add remove button event listener
        row.querySelector('.remove-header').addEventListener('click', () => {
          headersBody.removeChild(row);
        });

        headersBody.appendChild(row);
      });
    } else {
      // Add empty row
      const row = document.createElement('tr');
      row.innerHTML = `
        <td><input type="text" class="header-key"></td>
        <td><input type="text" class="header-value"></td>
        <td><button class="btn-small remove-header"><i class="fas fa-times"></i></button></td>
      `;

      // Add remove button event listener
      row.querySelector('.remove-header').addEventListener('click', () => {
        headersBody.removeChild(row);
      });

      headersBody.appendChild(row);
    }
  }

  // Helper function to populate params
  function populateParams(params) {
    const paramsBody = document.getElementById('params-body');
    if (!paramsBody) return;

    paramsBody.innerHTML = '';

    if (params && Object.keys(params).length > 0) {
      Object.entries(params).forEach(([key, value]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="text" class="param-key" value="${key}"></td>
          <td><input type="text" class="param-value" value="${value}"></td>
          <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
        `;

        // Add remove button event listener
        row.querySelector('.remove-param').addEventListener('click', () => {
          paramsBody.removeChild(row);
        });

        paramsBody.appendChild(row);
      });
    } else {
      // Add empty row
      const row = document.createElement('tr');
      row.innerHTML = `
        <td><input type="text" class="param-key"></td>
        <td><input type="text" class="param-value"></td>
        <td><button class="btn-small remove-param"><i class="fas fa-times"></i></button></td>
      `;

      // Add remove button event listener
      row.querySelector('.remove-param').addEventListener('click', () => {
        paramsBody.removeChild(row);
      });

      paramsBody.appendChild(row);
    }
  }

  // Helper function to populate body
  function populateBody(body) {
    if (!body) return;

    const bodyType = document.getElementById('body-type');
    if (!bodyType) return;

    // Set body type
    if (body.type) {
      bodyType.value = body.type;

      // Trigger change event to show the correct body container
      const event = new Event('change');
      bodyType.dispatchEvent(event);

      // Fill body content based on type
      if (body.type === 'raw') {
        const rawType = document.getElementById('raw-type');
        const rawBody = document.getElementById('raw-body');

        if (rawType) rawType.value = body.rawType || 'text';
        if (rawBody) rawBody.value = body.content || '';
      } else if (body.type === 'form-data') {
        const formDataBody = document.getElementById('form-data-body');
        if (formDataBody && body.content) {
          formDataBody.innerHTML = '';

          Object.entries(body.content).forEach(([key, value]) => {
            const row = document.createElement('tr');
            row.innerHTML = `
              <td><input type="text" class="form-key" value="${key}"></td>
              <td><input type="text" class="form-value" value="${value}"></td>
              <td><button class="btn-small remove-form"><i class="fas fa-times"></i></button></td>
            `;

            // Add remove button event listener
            row.querySelector('.remove-form').addEventListener('click', () => {
              formDataBody.removeChild(row);
            });

            formDataBody.appendChild(row);
          });
        }
      } else if (body.type === 'x-www-form-urlencoded') {
        const urlencodedBody = document.getElementById('urlencoded-body');
        if (urlencodedBody && body.content) {
          urlencodedBody.innerHTML = '';

          Object.entries(body.content).forEach(([key, value]) => {
            const row = document.createElement('tr');
            row.innerHTML = `
              <td><input type="text" class="urlencoded-key" value="${key}"></td>
              <td><input type="text" class="urlencoded-value" value="${value}"></td>
              <td><button class="btn-small remove-urlencoded"><i class="fas fa-times"></i></button></td>
            `;

            // Add remove button event listener
            row.querySelector('.remove-urlencoded').addEventListener('click', () => {
              urlencodedBody.removeChild(row);
            });

            urlencodedBody.appendChild(row);
          });
        }
      }
    }
  }

  // Helper function to display response
  function displayResponse(response) {
    if (!response) return;

    // Update status code
    const statusCodeEl = document.getElementById('status-code');
    if (statusCodeEl && response.status_code) {
      statusCodeEl.textContent = response.status_code;
      statusCodeEl.className = 'status-code';
      if (response.status_code >= 200 && response.status_code < 300) {
        statusCodeEl.classList.add('success');
      } else {
        statusCodeEl.classList.add('error');
      }
    }

    // Update response time
    const responseTimeEl = document.getElementById('response-time');
    if (responseTimeEl && response.response_time) {
      responseTimeEl.textContent = `${response.response_time} ms`;
    }

    // Update response body
    const responseBodyContentEl = document.getElementById('response-body-content');
    if (responseBodyContentEl && response.body) {
      if (typeof response.body === 'object') {
        responseBodyContentEl.textContent = JSON.stringify(response.body, null, 2);
      } else {
        responseBodyContentEl.textContent = response.body;
      }
    }

    // Update response headers
    const responseHeadersBodyEl = document.getElementById('response-headers-body');
    if (responseHeadersBodyEl && response.headers) {
      responseHeadersBodyEl.innerHTML = '';
      Object.entries(response.headers).forEach(([key, value]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${key}</td>
          <td>${value}</td>
        `;
        responseHeadersBodyEl.appendChild(row);
      });
    }
  }

  // Clear history button event listener
  if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener('click', clearAllHistory);
  }

  // Tab switching
  sidebarTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const tabId = tab.getAttribute('data-tab');

      // Remove active class from all tabs and contents
      sidebarTabs.forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.sidebar-content').forEach(content => {
        content.classList.remove('active');
      });

      // Add active class to clicked tab and content
      tab.classList.add('active');
      const tabContentEl = document.getElementById(`${tabId}-tab`);
      if (tabContentEl) tabContentEl.classList.add('active');

      // If history tab is activated, render history
      if (tabId === 'history') {
        renderHistory();
      }
    });
  });

  // Get user ID from token
  function getUserId() {
    const token = localStorage.getItem('token');
    if (!token) return null;

    try {
      // JWT tokens are in the format: header.payload.signature
      // We need the payload part
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      // Decode the payload (base64)
      const payload = JSON.parse(atob(parts[1]));
      return payload.id || null;
    } catch (error) {
      console.error('Error parsing token:', error);
      return null;
    }
  }

  // Function to add pagination controls
  function addPaginationControls(pagination, container) {
    const { page, totalPages, total, limit } = pagination;

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-controls';

    // Add pagination info
    const paginationInfo = document.createElement('div');
    paginationInfo.className = 'pagination-info';
    paginationInfo.textContent = `Showing ${(page - 1) * limit + 1}-${Math.min(page * limit, total)} of ${total} items`;
    paginationContainer.appendChild(paginationInfo);

    // Add pagination buttons
    const paginationButtons = document.createElement('div');
    paginationButtons.className = 'pagination-buttons';

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.className = 'pagination-button';
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.disabled = page <= 1;
    prevButton.addEventListener('click', () => {
      if (page > 1) {
        loadHistoryPage(page - 1, limit);
      }
    });
    paginationButtons.appendChild(prevButton);

    // Page number buttons
    const maxButtons = 5; // Maximum number of page buttons to show
    const startPage = Math.max(1, page - Math.floor(maxButtons / 2));
    const endPage = Math.min(totalPages, startPage + maxButtons - 1);

    for (let i = startPage; i <= endPage; i++) {
      const pageButton = document.createElement('button');
      pageButton.className = 'pagination-button';
      if (i === page) {
        pageButton.classList.add('active');
      }
      pageButton.textContent = i;
      pageButton.addEventListener('click', () => {
        if (i !== page) {
          loadHistoryPage(i, limit);
        }
      });
      paginationButtons.appendChild(pageButton);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.className = 'pagination-button';
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.disabled = page >= totalPages;
    nextButton.addEventListener('click', () => {
      if (page < totalPages) {
        loadHistoryPage(page + 1, limit);
      }
    });
    paginationButtons.appendChild(nextButton);

    paginationContainer.appendChild(paginationButtons);

    // Add pagination controls to the container
    container.appendChild(paginationContainer);
  }

  // Function to load a specific page of history
  function loadHistoryPage(page, limit) {
    if (!historyList) {
      console.error('History list element not found');
      return;
    }

    historyList.innerHTML = '';
    const loadingMessage = document.createElement('div');
    loadingMessage.className = 'loading-message';
    loadingMessage.textContent = 'Loading history...';
    historyList.appendChild(loadingMessage);

    const token = localStorage.getItem('token');
    if (!token) {
      showErrorMessage('You must be logged in to view history');
      return;
    }

    // Get user ID from token or localStorage
    const userId = getUserId();

    // Always use 123 as the workspace ID for history since that's where the data is stored
    const workspaceId = 123;

    // Prepare request data for the filter endpoint
    const requestData = {
      user_id: userId,
      workspace_id: workspaceId,
      limit: limit || 50,
      page: page || 1
    };

    console.log('Loading history page:', requestData);

    // Use the POST endpoint for filtering history
    fetch(`${API_URL}/history/filter`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(requestData)
    })
    .then(res => {
      if (!res.ok) throw new Error(`History API returned status: ${res.status}`);
      return res.json();
    })
    .then(response => {
      historyList.innerHTML = '';
      console.log('History API response for page', page, ':', response);

      // Extract history data and pagination info
      let historyItems = [];
      let pagination = { total: 0, page: page, limit: limit, totalPages: 1 };

      if (response && response.status === 'Success' && response.data) {
        console.log('Processing successful response with data:', response.data);

        if (response.data.items) {
          // New format with pagination
          historyItems = response.data.items;
          pagination = response.data.pagination || pagination;
          console.log('Extracted items:', historyItems.length, 'and pagination:', pagination);
        } else if (Array.isArray(response.data)) {
          // Old format without pagination
          historyItems = response.data;
          console.log('Using array data directly:', historyItems.length);
        } else {
          // Unknown format
          console.warn('Unknown response format:', response);
          historyItems = [];
        }
      } else if (Array.isArray(response)) {
        // Direct array response (old format)
        historyItems = response;
        console.log('Using direct array response:', historyItems.length);
      } else {
        console.error('Unexpected response format:', response);
      }

      if (!historyItems || historyItems.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-history-message';
        emptyMessage.textContent = 'No request history yet';
        historyList.appendChild(emptyMessage);
        return;
      }

      // Add pagination controls if we have multiple pages
      if (pagination.totalPages > 1) {
        addPaginationControls(pagination, historyList);
      }

      historyItems.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.setAttribute('data-id', item.id || item.api_request_history_Id);

        // Format the date
        const date = new Date(item.created_at);
        const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

        // Determine method
        const method = item.method || item.api_request_Method || 'GET';
        const methodClass = method.toLowerCase();

        // Determine URL
        const url = item.url || item.api_request_Url || 'Unknown URL';

        // Status code handling
        const statusCode = item.status_code || item.api_request_history_Status_Code;
        const statusText = statusCode ?
          `<div class="history-item-status ${statusCode >= 200 && statusCode < 300 ? 'success' : 'error'}">${statusCode}</div>`
          : '';

        historyItem.innerHTML = `
          <div class="history-item-method ${methodClass}">${method}</div>
          ${statusText}
          <div class="history-item-details">
            <div class="history-item-url">${url}</div>
            <div class="history-item-time">${formattedDate}</div>
          </div>
          <div class="history-item-actions">
            <button class="btn-icon delete-history-btn" title="Delete this history item">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        `;

        // Delete button event listener
        const deleteBtn = historyItem.querySelector('.delete-history-btn');
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          deleteHistoryItem(item.id || item.api_request_history_Id);
        });

        // Click event to load request from history
        historyItem.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();

          // Switch to the requests tab
          const requestsTab = document.querySelector('.sidebar-tab[data-tab="requests"]');
          if (requestsTab) {
            requestsTab.click();
          }

          // Load the request
          loadHistoryRequest(item);
        });

        historyList.appendChild(historyItem);
      });
    })
    .catch(err => {
      console.error('Error fetching history page:', err);
      handleError('loadHistoryPage', err);
      showErrorMessage(`Failed to load history: ${err.message}`);
    });
  }

  // Initial render of history
  renderHistory();
;