document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const addCollectionBtn = document.getElementById('add-collection-btn');
    const collectionModal = document.getElementById('collection-modal');
    const collectionForm = document.getElementById('collection-form');
    const collectionsListEl = document.getElementById('collections-list');
    const workspaceSelect = document.getElementById('workspace-select');

    // API URL
    const API_URL = '/api';

    // Event Listeners

    // Workspace selector change event
    if (workspaceSelect) {
      // Set initial value from localStorage
      const savedWorkspace = localStorage.getItem('current_workspace_id');
      if (savedWorkspace) {
        workspaceSelect.value = savedWorkspace;
      }

      workspaceSelect.addEventListener('change', () => {
        const selectedWorkspace = workspaceSelect.value;
        localStorage.setItem('current_workspace_id', selectedWorkspace);
        loadCollections(selectedWorkspace);
      });
    }

    addCollectionBtn.addEventListener('click', () => {
      // Reset form
      collectionForm.reset();

      // Update modal title
      document.querySelector('#collection-modal h2').textContent = 'Create Collection';

      // Remove collection ID attribute if exists
      collectionForm.removeAttribute('data-collection-id');

      // Set the workspace dropdown to the current workspace
      const workspaceDropdown = document.getElementById('collection-workspace');
      if (workspaceDropdown) {
        const currentWorkspace = localStorage.getItem('current_workspace_id') || '1';
        workspaceDropdown.value = currentWorkspace === 'all' ? '1' : currentWorkspace;
      }

      // Show modal
      collectionModal.style.display = 'block';
    });

    // Collection form submission
    collectionForm.addEventListener('submit', event => {
      event.preventDefault();

      const name = document.getElementById('collection-name').value;
      const description = document.getElementById('collection-description').value;
      const workspaceId = document.getElementById('collection-workspace').value;

      const token = localStorage.getItem('token');
      if (!token) {
        console.error('You must be logged in to create a collection');
        return;
      }

      const collectionId = collectionForm.getAttribute('data-collection-id');
      const isUpdate = !!collectionId;

      // Use the new POST endpoint for updating collections
      const endpoint = isUpdate ? `${API_URL}/collections/update/${collectionId}` : `${API_URL}/collections`;

      fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ name, description, workspace_id: workspaceId })
      })
      .then(res => res.json())
      .then(response => {
        console.log('Collection form response:', response);

        // Check if the response has the new format with status and message
        const data = response.data || response;

        if ((response.status === 'Success' && data) || data.id) {
          // Close modal
          collectionModal.style.display = 'none';

          // Reset form
          collectionForm.reset();

          // Check if this is an update or a new collection
          if (isUpdate) {
            console.log('Collection updated, ID:', data.id);

            // Find the collection element
            const collectionEl = document.querySelector(`.collection-item[data-id="${data.id}"]`);
            if (collectionEl) {
              // Update the collection name and description in the UI
              const nameEl = collectionEl.querySelector('.collection-name');
              const descEl = collectionEl.querySelector('.collection-description');

              if (nameEl) nameEl.textContent = data.name;
              if (descEl) descEl.textContent = data.description || '';

              // Check if the collection is active/expanded
              if (collectionEl.classList.contains('active')) {
                // Reload the collection content to preserve folders and requests
                window.loadCollection(data.id);
              }
            } else {
              // If we can't find the collection element, fall back to reloading all collections
              window.loadCollections();
            }
          } else {
            // For new collections, reload all collections
            window.loadCollections();
          }

          // Log success message instead of showing alert
          if (response.status === 'Success') {
            console.log(response.message || (isUpdate ? 'Collection updated successfully' : 'Collection created successfully'));
          }
        } else {
          console.error(response.message || data.message || 'Collection operation failed');
        }
      })
      .catch(err => {
        console.error(err);
        console.error('Collection creation failed. Please try again.');
      });
    });

    // Add request to collection
    window.addRequestToCollection = function(collectionId) {
      console.log('Adding request to collection:', collectionId);

      // Use the clearRequestForm function to reset the form
      if (window.clearRequestForm) {
        window.clearRequestForm();
      }

      // Set collection ID for the save button
      const saveBtn = document.getElementById('save-btn');
      if (saveBtn) {
        // Set collection ID and remove folder ID
        saveBtn.setAttribute('data-collection-id', collectionId);
        saveBtn.removeAttribute('data-folder-id');
      }

      // Show requests container and hide auth container
      const authContainer = document.getElementById('auth-container');
      const requestsContainer = document.getElementById('requests-container');

      if (authContainer) {
        authContainer.classList.add('hidden');
      }

      if (requestsContainer) {
        requestsContainer.classList.remove('hidden');
      }

      // Activate params tab by default
      const paramsTabBtn = document.querySelector('.tab-btn[data-tab="params"]');
      const paramsTab = document.getElementById('params-tab');

      if (paramsTabBtn) {
        paramsTabBtn.classList.add('active');
      }

      if (paramsTab) {
        paramsTab.classList.add('active');
      }
    };

    // Load collection with folders and requests
    window.loadCollection = function(collectionId) {
      console.log('Loading collection:', collectionId);
      const token = localStorage.getItem('token');
      if (!token) return;

      fetch(`${API_URL}/collections/${collectionId}`, {
        headers: {
          'x-auth-token': token
        }
      })
      .then(res => {
        if (!res.ok) {
          console.error('Collection fetch failed with status:', res.status);
          throw new Error(`Failed to fetch collection: ${res.status}`);
        }
        return res.json();
      })
      .then(response => {
        console.log('Collection response:', response);

        // Check if the response has the new format with status and message
        const collection = response.data || response;

        console.log('Collection data:', collection);

        // Validate collection data
        if (!collection || !collection.id) {
          console.error('Invalid collection data:', collection);
          throw new Error('Invalid collection data received');
        }

        // Find the collection element
        const collectionEl = document.querySelector(`.collection-item[data-id="${collectionId}"]`);
        if (!collectionEl) {
          console.error('Collection element not found for ID:', collectionId);
          return;
        }

        // Set active collection
        document.querySelectorAll('.collection-item').forEach(item => {
          item.classList.remove('active');
        });
        collectionEl.classList.add('active');

        // Store expanded folders state before clearing content
        const expandedFolders = new Set();
        const folderElements = collectionEl.querySelectorAll('.folder-item.expanded');
        folderElements.forEach(folder => {
          const folderId = folder.getAttribute('data-id');
          if (folderId) {
            expandedFolders.add(folderId);
          }
        });

        console.log('Expanded folders:', Array.from(expandedFolders));

        // Clear existing content
        const existingContent = collectionEl.querySelector('.collection-content');
        if (existingContent) {
          collectionEl.removeChild(existingContent);
        }

        // Create collection content container
        const collectionContent = document.createElement('div');
        collectionContent.className = 'collection-content';

        // Add folders
        if (collection.folders && collection.folders.length > 0) {
          console.log('Rendering', collection.folders.length, 'folders');
          collection.folders.forEach(folder => {
            renderFolder(folder, collectionContent, expandedFolders);
          });
        }

        // Add requests
        if (collection.requests && collection.requests.length > 0) {
          console.log('Rendering', collection.requests.length, 'requests');
          const requestsList = document.createElement('div');
          requestsList.className = 'requests-list';

          collection.requests.forEach(request => {
            renderRequest(request, requestsList);
          });

          collectionContent.appendChild(requestsList);
        }

        collectionEl.appendChild(collectionContent);
      })
      .catch(err => {
        console.error('Error loading collection:', err);

        // Find the collection element
        const collectionEl = document.querySelector(`.collection-item[data-id="${collectionId}"]`);
        if (collectionEl) {
          // Clear existing content
          const existingContent = collectionEl.querySelector('.collection-content');
          if (existingContent) {
            collectionEl.removeChild(existingContent);
          }

          // Create error message
          const errorContent = document.createElement('div');
          errorContent.className = 'collection-content';
          errorContent.innerHTML = `
            <div class="error-message">
              <p>Error loading collection: ${err.message}</p>
              <button class="btn retry-btn">Retry</button>
            </div>
          `;

          // Add retry button event listener
          const retryBtn = errorContent.querySelector('.retry-btn');
          if (retryBtn) {
            retryBtn.addEventListener('click', () => {
              window.loadCollection(collectionId);
            });
          }

          collectionEl.appendChild(errorContent);
        }
      });
    };

    // Load collections
    window.loadCollections = function(workspaceId = 1) {
      const token = localStorage.getItem('token');
      if (!token) return;

      // Get the current workspace ID from localStorage or use the provided one
      const currentWorkspaceId = workspaceId || localStorage.getItem('current_workspace_id') || 1;

      // Store the current workspace ID
      localStorage.setItem('current_workspace_id', currentWorkspaceId);

      console.log('Fetching collections for workspace:', currentWorkspaceId);
      console.log('Token exists:', !!token);
      console.log('Token length:', token.length);

      fetch(`${API_URL}/collections/list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ workspace_id: currentWorkspaceId })
      })
      .then(res => {
        console.log('Collections API response status:', res.status);
        if (!res.ok) {
          console.error('Collections fetch failed with status:', res.status);
          throw new Error(`Failed to fetch collections: ${res.status}`);
        }
        return res.json();
      })
      .then(response => {
        console.log('Collections response:', response);

        // Check if the response has the new format with status and message
        const collections = response.data || response;

        // Validate collections data
        if (!collections) {
          console.error('Invalid collections data:', collections);
          throw new Error('Invalid collections data received');
        }

        // Clear collections list
        collectionsListEl.innerHTML = '';

        // Check if collections is an array
        if (!Array.isArray(collections)) {
          console.error('Collections is not an array:', collections);
          return;
        }

        // Add collections to the list
        collections.forEach(collection => {
          const collectionEl = document.createElement('div');
          collectionEl.className = 'collection-item';
          collectionEl.setAttribute('data-id', collection.id);

          collectionEl.innerHTML = `
            <div class="collection-header">
              <div class="collection-info">
                <div class="collection-name">${collection.name}</div>
                <div class="collection-description">${collection.description || ''}</div>
              </div>
              <div class="collection-actions">
                <button class="btn-small collection-add-folder" title="Add Folder"><i class="fas fa-folder-plus"></i></button>
                <button class="btn-small collection-add-request" title="Add Request"><i class="fas fa-plus"></i></button>
                <button class="btn-small collection-edit" title="Edit Collection"><i class="fas fa-edit"></i></button>
                <button class="btn-small collection-delete" title="Delete Collection"><i class="fas fa-trash"></i></button>
              </div>
            </div>
          `;

          // Add folder button
          collectionEl.querySelector('.collection-add-folder').addEventListener('click', (e) => {
            e.stopPropagation();
            addFolderToCollection(collection.id);
          });

          // Add request button
          collectionEl.querySelector('.collection-add-request').addEventListener('click', (e) => {
            e.stopPropagation();
            addRequestToCollection(collection.id);
          });

          // Edit collection button
          collectionEl.querySelector('.collection-edit').addEventListener('click', (e) => {
            e.stopPropagation();
            // Reset form
            collectionForm.reset();

            // Update modal title
            document.querySelector('#collection-modal h2').textContent = 'Edit Collection';

            // Fill form
            document.getElementById('collection-name').value = collection.name;
            document.getElementById('collection-description').value = collection.description || '';

            // Set workspace value if available, otherwise default to 1
            const workspaceSelect = document.getElementById('collection-workspace');
            if (workspaceSelect) {
              const workspaceId = collection.workspace_id || 1;

              // Check if the option exists
              const option = workspaceSelect.querySelector(`option[value="${workspaceId}"]`);
              if (option) {
                workspaceSelect.value = workspaceId;
              } else {
                // Default to first option if the workspace doesn't exist in the dropdown
                workspaceSelect.selectedIndex = 0;
              }
            }

            // Set collection ID for update
            collectionForm.setAttribute('data-collection-id', collection.id);

            // Show modal
            collectionModal.style.display = 'block';
          });

          // Delete collection button
          collectionEl.querySelector('.collection-delete').addEventListener('click', (e) => {
            e.stopPropagation();
            // Remove confirmation dialog and proceed directly
            const token = localStorage.getItem('token');
            if (!token) return;

            fetch(`${API_URL}/collections/${collection.id}`, {
              method: 'DELETE',
              headers: {
                'x-auth-token': token
              }
            })
            .then(res => res.json())
            .then(response => {
              console.log('Delete collection response:', response);

              // Check if the response has the new format with status and message
              if (response.status === 'Success' || response.message === 'Collection deleted') {
                window.loadCollections();
              } else {
                console.error(response.message || 'Collection deletion failed');
              }
            })
            .catch(err => {
              console.error(err);
              console.error('Collection deletion failed. Please try again.');
            });
          });

          // Click event to load collection
          collectionEl.querySelector('.collection-header').addEventListener('click', () => {
            // Set active collection
            document.querySelectorAll('.collection-item').forEach(item => {
              item.classList.remove('active');
            });
            collectionEl.classList.add('active');

            // Load collection with folders and requests
            window.loadCollection(collection.id);
          });

          collectionsListEl.appendChild(collectionEl);
        });
      })
      .catch(err => {
        console.error('Error loading collections:', err);

        // Show error message in collections list
        collectionsListEl.innerHTML = `
          <div class="error-message">
            <p>Error loading collections: ${err.message}</p>
            <button class="btn retry-btn">Retry</button>
          </div>
        `;

        // Add retry button event listener
        const retryBtn = collectionsListEl.querySelector('.retry-btn');
        if (retryBtn) {
          retryBtn.addEventListener('click', () => {
            window.loadCollections(currentWorkspaceId);
          });
        }
      });
    };
  });