document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const collectionsListEl = document.getElementById('collections-list');

  // API URL
  const API_URL = '/api';

  // Create folder modal
  const folderModal = document.createElement('div');
  folderModal.id = 'folder-modal';
  folderModal.className = 'modal';
  folderModal.innerHTML = `
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>Create Folder</h2>
      <form id="folder-form">
        <div class="form-group">
          <label for="folder-name">Name</label>
          <input type="text" id="folder-name" required>
        </div>
        <div class="form-group">
          <label for="folder-description">Description</label>
          <textarea id="folder-description"></textarea>
        </div>
        <input type="hidden" id="folder-collection-id">
        <input type="hidden" id="folder-parent-id">
        <button type="submit" class="btn">Create</button>
      </form>
    </div>
  `;
  document.body.appendChild(folderModal);

  // Close modal when clicking on X
  folderModal.querySelector('.close').addEventListener('click', () => {
    folderModal.style.display = 'none';
  });

  // Close modal when clicking outside
  window.addEventListener('click', (event) => {
    if (event.target === folderModal) {
      folderModal.style.display = 'none';
    }
  });

  // Folder form submission
  const folderForm = document.getElementById('folder-form');
  folderForm.addEventListener('submit', event => {
    event.preventDefault();

    const name = document.getElementById('folder-name').value;
    const description = document.getElementById('folder-description').value;
    const collectionId = document.getElementById('folder-collection-id').value;
    const parentFolderId = document.getElementById('folder-parent-id').value;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('You must be logged in to create a folder');
      return;
    }

    const folderId = folderForm.getAttribute('data-folder-id');
    const isUpdate = !!folderId;

    const folderData = {
      name,
      description
    };

    if (collectionId) {
      folderData.collection_id = collectionId;
      // For top-level folders, don't set parent_folder_id (it will be NULL)
      // This is important because the database has a foreign key constraint
    }

    if (parentFolderId) {
      folderData.parent_folder_id = parentFolderId;
    }

    console.log('Sending folder data:', folderData);

    // Use the new POST endpoint for updating folders
    const endpoint = isUpdate ? `${API_URL}/folders/update/${folderId}` : `${API_URL}/folders`;

    fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(folderData)
    })
    .then(res => {
      console.log('Folder save response status:', res.status);
      return res.json();
    })
    .then(response => {
      console.log('Folder save response:', response);

      // Extract the folder data from the response
      const data = response.data || response;

      if (data && data.id) {
        // Close modal
        folderModal.style.display = 'none';

        // Reset form
        folderForm.reset();

        console.log('Folder saved successfully. ID:', data.id);
        console.log('Collection ID:', data.collection_id || collectionId, 'Parent Folder ID:', data.parent_folder_id || parentFolderId);

        // Check if this is an update
        if (isUpdate) {
          // Find the folder element
          const folderEl = document.querySelector(`.folder-item[data-id="${folderId}"]`);
          if (folderEl) {
            // Update the folder name in the UI
            const nameEl = folderEl.querySelector('.folder-name');
            if (nameEl) {
              nameEl.textContent = data.name || name;
            }

            // Check if the folder is expanded
            const isExpanded = folderEl.classList.contains('expanded');

            // If the folder is expanded, reload its content
            if (isExpanded) {
              loadFolder(folderId);
            }

            // If the folder has a parent, reload the parent to update the UI
            if (parentFolderId) {
              const parentEl = folderEl.closest(`.folder-item[data-id="${parentFolderId}"]`);
              if (parentEl && parentEl.classList.contains('expanded')) {
                loadFolder(parentFolderId);
              }
            } else if (collectionId) {
              const collectionEl = folderEl.closest(`.collection-item[data-id="${collectionId}"]`);
              if (collectionEl && collectionEl.classList.contains('active')) {
                loadCollection(collectionId);
              }
            }
          } else {
            // If we can't find the folder element, fall back to reloading the parent
            if (collectionId) {
              loadCollection(collectionId);
            } else if (parentFolderId) {
              loadFolder(parentFolderId);
            }
          }
        } else {
          // For new folders, reload the parent
          if (collectionId) {
            loadCollection(collectionId);
          } else if (parentFolderId) {
            loadFolder(parentFolderId);
          }
        }
      } else {
        console.error(data.message || 'Folder creation failed');
      }
    })
    .catch(err => {
      console.error(err);
      console.error('Folder creation failed. Please try again.');
    });
  });

  // Add folder to collection
  window.addFolderToCollection = function(collectionId) {
    // Reset form
    folderForm.reset();

    // Update modal title
    document.querySelector('#folder-modal h2').textContent = 'Create Folder';

    // Set collection ID
    document.getElementById('folder-collection-id').value = collectionId;
    document.getElementById('folder-parent-id').value = '';

    // Remove folder ID attribute if exists
    folderForm.removeAttribute('data-folder-id');

    // Show modal
    folderModal.style.display = 'block';
  };

  // Add subfolder to folder
  window.addSubfolder = function(parentFolderId) {
    // Reset form
    folderForm.reset();

    // Update modal title
    document.querySelector('#folder-modal h2').textContent = 'Create Subfolder';

    // Set parent folder ID
    document.getElementById('folder-parent-id').value = parentFolderId;
    document.getElementById('folder-collection-id').value = '';

    // Remove folder ID attribute if exists
    folderForm.removeAttribute('data-folder-id');

    // Show modal
    folderModal.style.display = 'block';
  };

  // Edit folder
  window.editFolder = function(folderId) {
    console.log('Editing folder:', folderId);
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`${API_URL}/folders/${folderId}`, {
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      console.log('Folder fetch response status:', res.status);
      return res.json();
    })
    .then(response => {
      console.log('Folder fetch response:', response);

      // Extract the folder data from the response
      const folder = response.data || response;

      if (!folder || !folder.id) {
        console.error('Invalid folder data received:', response);
        throw new Error('Invalid folder data received');
      }

      console.log('Folder data for editing:', folder);

      // Reset form
      folderForm.reset();

      // Update modal title
      document.querySelector('#folder-modal h2').textContent = 'Edit Folder';

      // Fill form
      document.getElementById('folder-name').value = folder.name;
      document.getElementById('folder-description').value = folder.description || '';

      // Set collection ID or parent folder ID
      if (folder.collection_id) {
        document.getElementById('folder-collection-id').value = folder.collection_id;
        // For top-level folders, leave parent_folder_id empty
        document.getElementById('folder-parent-id').value = folder.parent_folder_id || '';
      } else if (folder.parent_folder_id) {
        document.getElementById('folder-parent-id').value = folder.parent_folder_id;
        document.getElementById('folder-collection-id').value = '';
      } else {
        // Default values
        document.getElementById('folder-parent-id').value = '';
        document.getElementById('folder-collection-id').value = '';
      }

      // Set folder ID for update
      folderForm.setAttribute('data-folder-id', folder.id);

      // Show modal
      folderModal.style.display = 'block';
    })
    .catch(err => {
      console.error('Error loading folder details:', err);
      console.error('Failed to load folder details');
    });
  };

  // Delete folder
  window.deleteFolder = function(folderId) {
    // Remove confirmation dialog and proceed directly

    const token = localStorage.getItem('token');
    if (!token) return;

    // Use the new POST endpoint for deleting folders
    fetch(`${API_URL}/folders/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify({ folder_id: folderId })
    })
    .then(res => res.json())
    .then(data => {
      if (data.status === 'Success' || data.message === 'Folder deleted successfully') {
        // Find the folder element and get its parent
        const folderEl = document.querySelector(`.folder-item[data-id="${folderId}"]`);
        if (folderEl) {
          const parentEl = folderEl.closest('.collection-item, .folder-item');
          if (parentEl) {
            if (parentEl.classList.contains('collection-item')) {
              loadCollection(parentEl.getAttribute('data-id'));
            } else {
              loadFolder(parentEl.getAttribute('data-id'));
            }
          }
        }
      } else {
        console.error(data.message || 'Folder deletion failed');
      }
    })
    .catch(err => {
      console.error(err);
      console.error('Folder deletion failed. Please try again.');
    });
  };

  // Load folder
  window.loadFolder = function(folderId) {
    console.log('Loading folder:', folderId);
    const token = localStorage.getItem('token');
    if (!token) return;

    // Find the folder element first
    const folderEl = document.querySelector(`.folder-item[data-id="${folderId}"]`);
    if (!folderEl) {
      console.error('Folder element not found for ID:', folderId);
      return;
    }

    // Show loading indicator
    const folderContent = folderEl.querySelector('.folder-content') || document.createElement('div');
    if (!folderEl.contains(folderContent)) {
      folderContent.className = 'folder-content';
      folderEl.appendChild(folderContent);
    }
    folderContent.innerHTML = '<div class="loading">Loading folder content...</div>';

    // Set active folder
    document.querySelectorAll('.folder-item').forEach(item => {
      item.classList.remove('active');
    });
    folderEl.classList.add('active');

    // Expand folder to show loading indicator
    folderEl.classList.add('expanded');

    console.log('Fetching folder data from API:', folderId);
    fetch(`${API_URL}/folders/${folderId}`, {
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      if (!res.ok) {
        console.error('Folder fetch failed with status:', res.status);
        throw new Error(`Failed to fetch folder: ${res.status}`);
      }
      return res.json();
    })
    .then(response => {
      console.log('Folder API response:', response);

      // Check if the response has the expected format
      const folder = response.data || response;

      if (!folder || !folder.id) {
        console.error('Invalid folder data:', folder);
        throw new Error('Invalid folder data received');
      }

      // Clear existing content
      folderContent.innerHTML = '';

      // Add subfolders
      if (folder.folders && folder.folders.length > 0) {
        console.log('Rendering', folder.folders.length, 'subfolders');
        folder.folders.forEach(subfolder => {
          renderFolder(subfolder, folderContent);
        });
      }

      // Add requests
      if (folder.requests && folder.requests.length > 0) {
        console.log('Rendering', folder.requests.length, 'requests');
        folder.requests.forEach(request => {
          renderRequest(request, folderContent);
        });
      } else {
        console.log('No requests to render for folder:', folderId);

        // Add empty state message if there are no subfolders either
        if (!folder.folders || folder.folders.length === 0) {
          const emptyState = document.createElement('div');
          emptyState.className = 'empty-state';
          emptyState.innerHTML = '<p>This folder is empty</p>';
          folderContent.appendChild(emptyState);
        }
      }
    })
    .catch(err => {
      console.error('Error loading folder:', err);

      // Show error message in folder content
      folderContent.innerHTML = `
        <div class="error-message">
          <p>Error loading folder: ${err.message}</p>
          <button class="btn retry-btn">Retry</button>
        </div>
      `;

      // Add retry button event listener
      const retryBtn = folderContent.querySelector('.retry-btn');
      if (retryBtn) {
        retryBtn.addEventListener('click', () => {
          window.loadFolder(folderId);
        });
      }
    });
  };

  // Render folder
  window.renderFolder = function(folder, parentElement, expandedFolders = new Set()) {
    console.log('Rendering folder:', folder.id, folder.name);

    // Check if folder has valid data
    if (!folder || !folder.id) {
      console.error('Invalid folder data:', folder);
      return;
    }

    // Check if folder already exists in the DOM
    const existingFolder = parentElement.querySelector(`.folder-item[data-id="${folder.id}"]`);
    if (existingFolder) {
      console.log('Folder already exists in DOM, updating:', folder.id);
      // Update folder name if it changed
      const nameEl = existingFolder.querySelector('.folder-name');
      if (nameEl && nameEl.textContent !== folder.name) {
        nameEl.textContent = folder.name;
      }
      return existingFolder;
    }

    const folderEl = document.createElement('div');
    folderEl.className = 'folder-item';
    folderEl.setAttribute('data-id', folder.id);

    // Check if this folder was previously expanded
    const wasExpanded = expandedFolders.has(folder.id);
    if (wasExpanded) {
      console.log('Folder was previously expanded:', folder.id);
      folderEl.classList.add('expanded');
    }

    folderEl.innerHTML = `
      <div class="folder-header">
        <span class="folder-icon"><i class="fas fa-folder"></i></span>
        <span class="folder-name">${folder.name}</span>
        <div class="folder-actions">
          <button class="btn-small folder-add-subfolder" title="Add Subfolder"><i class="fas fa-folder-plus"></i></button>
          <button class="btn-small folder-add-request" title="Add Request"><i class="fas fa-plus"></i></button>
          <button class="btn-small folder-edit" title="Edit Folder"><i class="fas fa-edit"></i></button>
          <button class="btn-small folder-delete" title="Delete Folder"><i class="fas fa-trash"></i></button>
        </div>
      </div>
    `;

    // Add subfolder button
    folderEl.querySelector('.folder-add-subfolder').addEventListener('click', (e) => {
      e.stopPropagation();
      addSubfolder(folder.id);
    });

    // Add request button
    folderEl.querySelector('.folder-add-request').addEventListener('click', (e) => {
      e.stopPropagation();
      console.log('Add request button clicked for folder:', folder.id);

      // Direct implementation to show the request modal
      try {
        // Get the modal and form elements directly
        const modal = document.getElementById('request-modal');
        const form = document.getElementById('request-form');
        const nameInput = document.getElementById('request-modal-name');
        const methodInput = document.getElementById('request-modal-method');
        const urlInput = document.getElementById('request-modal-url');
        const collectionIdInput = document.getElementById('request-modal-collection-id');
        const folderIdInput = document.getElementById('request-modal-folder-id');

        console.log('Modal elements found:', {
          modal: !!modal,
          form: !!form,
          nameInput: !!nameInput,
          methodInput: !!methodInput,
          urlInput: !!urlInput,
          collectionIdInput: !!collectionIdInput,
          folderIdInput: !!folderIdInput
        });

        if (!modal || !form) {
          console.error('Required modal elements not found');
          return;
        }

        // Reset form
        form.reset();

        // Update modal title
        const titleEl = modal.querySelector('h2');
        if (titleEl) {
          titleEl.textContent = 'Create Request in Folder';
        }

        // Set folder ID and clear collection ID
        if (folderIdInput) folderIdInput.value = folder.id;
        if (collectionIdInput) collectionIdInput.value = '';

        // Set default values for hidden fields
        if (methodInput) methodInput.value = 'GET';
        if (urlInput) urlInput.value = 'https://example.com';

        // Add a data attribute to track the source
        form.setAttribute('data-source', 'folder');

        // Show modal
        console.log('Setting modal display to block');
        modal.style.display = 'block';

        // Force modal to be visible with inline styles
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
        modal.style.zIndex = '1000';

        // Add a class to ensure visibility
        modal.classList.add('visible');

        // Focus on the name input
        if (nameInput) {
          setTimeout(() => {
            nameInput.focus();
          }, 100);
        }

        console.log('Modal display style after setting:', modal.style.display);
      } catch (error) {
        console.error('Error showing request modal:', error);
      }
    });

    // Edit folder button
    folderEl.querySelector('.folder-edit').addEventListener('click', (e) => {
      e.stopPropagation();
      editFolder(folder.id);
    });

    // Delete folder button
    folderEl.querySelector('.folder-delete').addEventListener('click', (e) => {
      e.stopPropagation();
      deleteFolder(folder.id);
    });

    // Click event to toggle folder
    folderEl.querySelector('.folder-header').addEventListener('click', () => {
      if (folderEl.classList.contains('expanded')) {
        folderEl.classList.remove('expanded');
      } else {
        loadFolder(folder.id);
      }
    });

    // Add folder content container
    const folderContent = document.createElement('div');
    folderContent.className = 'folder-content';
    folderEl.appendChild(folderContent);

    // Add subfolders if they exist
    if (folder.folders && Array.isArray(folder.folders) && folder.folders.length > 0) {
      console.log('Rendering', folder.folders.length, 'subfolders for folder:', folder.id);
      folder.folders.forEach(subfolder => {
        try {
          renderFolder(subfolder, folderContent, expandedFolders);
        } catch (err) {
          console.error('Error rendering subfolder:', err);
        }
      });
    }

    // Add requests if they exist
    if (folder.requests && Array.isArray(folder.requests) && folder.requests.length > 0) {
      console.log('Rendering', folder.requests.length, 'requests for folder:', folder.id);
      folder.requests.forEach(request => {
        try {
          renderRequest(request, folderContent);
        } catch (err) {
          console.error('Error rendering request:', err);
        }
      });
    }

    // If the folder was expanded, load its content
    if (wasExpanded) {
      // We need to load the folder content to ensure it's up to date
      setTimeout(() => {
        loadFolder(folder.id);
      }, 100);
    }

    parentElement.appendChild(folderEl);
    return folderEl;
  };

  // Render request
  window.renderRequest = function(request, parentElement) {
    console.log('Rendering request:', request);

    // Check if request has valid data
    if (!request) {
      console.error('Invalid request data: null or undefined');
      return;
    }

    // Extract the data from the response if needed
    const requestData = request.data || request;

    if (!requestData || !requestData.id) {
      console.error('Invalid request data:', requestData);
      return;
    }

    // Use requestData for the rest of the function
    request = requestData;

    // Check if request already exists in the DOM
    const existingRequest = parentElement.querySelector(`.request-item[data-id="${request.id}"]`);
    if (existingRequest) {
      console.log('Request already exists in DOM, updating:', request.id);
      // Update request name and method if they changed
      const nameEl = existingRequest.querySelector('.request-item-name');
      const methodEl = existingRequest.querySelector('.request-item-method');

      if (nameEl && nameEl.textContent !== request.name) {
        nameEl.textContent = request.name;
      }

      if (methodEl && methodEl.textContent !== request.method) {
        methodEl.textContent = request.method;

        // Update method class
        methodEl.className = 'request-item-method';
        if (request.method) {
          methodEl.classList.add(request.method.toLowerCase());
        }
      }

      return existingRequest;
    }

    const requestEl = document.createElement('div');
    requestEl.className = 'request-item';
    requestEl.setAttribute('data-id', request.id);

    // Ensure method and name are strings
    const method = request.method || 'GET';
    const name = request.name || 'Unnamed Request';

    requestEl.innerHTML = `
      <div class="request-item-method ${method.toLowerCase()}">${method}</div>
      <div class="request-item-name">${name}</div>
      <div class="request-item-actions">
        <button class="btn-icon request-rename" title="Rename Request"><i class="fas fa-edit"></i></button>
        <button class="btn-icon request-duplicate" title="Duplicate Request"><i class="fas fa-copy"></i></button>
        <button class="btn-icon request-delete" title="Delete Request"><i class="fas fa-trash-alt"></i></button>
      </div>
    `;

    // Click event to load request
    requestEl.addEventListener('click', (e) => {
      // Prevent default behavior to avoid navigation
      e.preventDefault();
      e.stopPropagation();

      // Don't load the request if an action button was clicked
      if (e.target.closest('.request-item-actions')) {
        return;
      }

      console.log('Request clicked:', request.id);

      // Show requests container and hide auth container
      const authContainer = document.getElementById('auth-container');
      const requestsContainer = document.getElementById('requests-container');

      if (authContainer) {
        authContainer.classList.add('hidden');
      }

      if (requestsContainer) {
        requestsContainer.classList.remove('hidden');
      }

      // Load the request
      if (window.loadRequest) {
        window.loadRequest(request.id);
      } else {
        console.error('loadRequest function not found');
      }
    });

    // Rename request button
    requestEl.querySelector('.request-rename').addEventListener('click', (e) => {
      e.stopPropagation();
      renameRequest(request.id, request.name);
    });

    // Duplicate request button
    requestEl.querySelector('.request-duplicate').addEventListener('click', (e) => {
      e.stopPropagation();
      duplicateRequest(request.id);
    });

    // Delete request button
    requestEl.querySelector('.request-delete').addEventListener('click', (e) => {
      e.stopPropagation();
      deleteRequest(request.id);
    });

    parentElement.appendChild(requestEl);
    return requestEl;
  };

  // Rename request
  window.renameRequest = function(requestId, currentName) {
    console.log('Opening rename modal for request:', requestId, currentName);

    // Get the rename modal elements
    const renameModal = document.getElementById('rename-request-modal');
    const renameForm = document.getElementById('rename-request-form');
    const renameInput = document.getElementById('rename-request-name');
    const renameIdInput = document.getElementById('rename-request-id');
    const closeBtn = renameModal.querySelector('.close');

    // Set the current name and ID in the form
    renameInput.value = currentName;
    renameIdInput.value = requestId;

    // Show the modal
    renameModal.style.display = 'block';

    // Focus the input field
    setTimeout(() => {
      renameInput.focus();
      // Select the text to make it easy to change
      renameInput.select();
    }, 100);

    // Close button event
    closeBtn.onclick = function() {
      renameModal.style.display = 'none';
    };

    // Close modal when clicking outside
    window.onclick = function(event) {
      if (event.target === renameModal) {
        renameModal.style.display = 'none';
      }
    };

    // Form submit event
    const submitHandler = function(e) {
      e.preventDefault();

      const newName = renameInput.value;
      const requestId = renameIdInput.value;

      if (!newName || newName.trim() === '') {
        alert('Please enter a name for the request');
        return;
      }

      // Hide the modal
      renameModal.style.display = 'none';

      // Get token
      const token = localStorage.getItem('token');
      if (!token) return;

      // Show a loading notification
      const loadingNotification = document.createElement('div');
      loadingNotification.className = 'notification loading';
      loadingNotification.textContent = `Renaming request...`;
      loadingNotification.style.position = 'fixed';
      loadingNotification.style.top = '20px';
      loadingNotification.style.left = '50%';
      loadingNotification.style.transform = 'translateX(-50%)';
      loadingNotification.style.padding = '10px 20px';
      loadingNotification.style.backgroundColor = '#007bff';
      loadingNotification.style.color = 'white';
      loadingNotification.style.borderRadius = '4px';
      loadingNotification.style.zIndex = '1000';
      loadingNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

      // Add the notification to the body
      document.body.appendChild(loadingNotification);

      // Send the rename request
      fetch(`${API_URL}/requests/${requestId}/rename`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ name: newName })
      })
      .then(res => {
        // Remove the loading notification
        document.body.removeChild(loadingNotification);

        console.log('Rename request response status:', res.status);
        if (!res.ok) {
          throw new Error(`HTTP error! Status: ${res.status}`);
        }
        return res.json();
      })
      .then(response => {
        console.log('Rename request response:', response);

        // Extract the data from the response
        const data = response.data || response;

        if (data && data.id) {
          console.log('Request renamed successfully:', data);

          // Create a success notification
          const notification = document.createElement('div');
          notification.className = 'notification success';
          notification.textContent = `Request renamed successfully!`;
          notification.style.position = 'fixed';
          notification.style.top = '20px';
          notification.style.left = '50%';
          notification.style.transform = 'translateX(-50%)';
          notification.style.padding = '10px 20px';
          notification.style.backgroundColor = 'var(--success-color)';
          notification.style.color = 'white';
          notification.style.borderRadius = '4px';
          notification.style.zIndex = '1000';
          notification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

          // Add the notification to the body
          document.body.appendChild(notification);

          // Remove the notification after 3 seconds
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 3000);

          // Update the request name in the UI
          const requestEl = document.querySelector(`.request-item[data-id="${requestId}"]`);
          if (requestEl) {
            const nameEl = requestEl.querySelector('.request-item-name');
            if (nameEl) {
              nameEl.textContent = newName;
            }
          }

          // If the request is currently loaded, update the name in the form
          const saveBtn = document.getElementById('save-btn');
          if (saveBtn && saveBtn.getAttribute('data-request-id') === requestId.toString()) {
            const requestNameInput = document.getElementById('request-name');
            if (requestNameInput) {
              requestNameInput.value = newName;
            }
          }
        } else {
          // Create an error notification
          const errorNotification = document.createElement('div');
          errorNotification.className = 'notification error';
          errorNotification.textContent = `Error renaming request: ${data.message || 'Unknown error'}`;
          errorNotification.style.position = 'fixed';
          errorNotification.style.top = '20px';
          errorNotification.style.left = '50%';
          errorNotification.style.transform = 'translateX(-50%)';
          errorNotification.style.padding = '10px 20px';
          errorNotification.style.backgroundColor = 'var(--danger-color)';
          errorNotification.style.color = 'white';
          errorNotification.style.borderRadius = '4px';
          errorNotification.style.zIndex = '1000';
          errorNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

          // Add the notification to the body
          document.body.appendChild(errorNotification);

          // Remove the notification after 5 seconds
          setTimeout(() => {
            document.body.removeChild(errorNotification);
          }, 5000);

          console.error(data.message || 'Failed to rename request');
        }
      })
      .catch(err => {
        // Remove the loading notification if it still exists
        if (document.body.contains(loadingNotification)) {
          document.body.removeChild(loadingNotification);
        }

        // Create an error notification
        const errorNotification = document.createElement('div');
        errorNotification.className = 'notification error';
        errorNotification.textContent = `Error renaming request: ${err.message || 'Unknown error'}`;
        errorNotification.style.position = 'fixed';
        errorNotification.style.top = '20px';
        errorNotification.style.left = '50%';
        errorNotification.style.transform = 'translateX(-50%)';
        errorNotification.style.padding = '10px 20px';
        errorNotification.style.backgroundColor = 'var(--danger-color)';
        errorNotification.style.color = 'white';
        errorNotification.style.borderRadius = '4px';
        errorNotification.style.zIndex = '1000';
        errorNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Add the notification to the body
        document.body.appendChild(errorNotification);

        // Remove the notification after 5 seconds
        setTimeout(() => {
          document.body.removeChild(errorNotification);
        }, 5000);

        console.error('Error renaming request:', err);
      });

      // Remove the event listener to prevent multiple submissions
      renameForm.removeEventListener('submit', submitHandler);
    };

    // Add submit event listener
    renameForm.addEventListener('submit', submitHandler);
  };

  // Duplicate request
  window.duplicateRequest = function(requestId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`${API_URL}/requests/${requestId}/duplicate`, {
      method: 'POST',
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => {
      console.log('Duplicate request response status:', res.status);
      return res.json();
    })
    .then(response => {
      console.log('Duplicate request response:', response);

      // Extract the data from the response
      const data = response.data || response;

      if (data && data.id) {
        console.log('Request duplicated successfully:', data);

        // Find the parent element (collection or folder)
        const requestEl = document.querySelector(`.request-item[data-id="${requestId}"]`);
        if (requestEl) {
          const parentEl = requestEl.closest('.collection-content, .folder-content');
          if (parentEl) {
            // Render the new request
            renderRequest(data, parentEl);
          }
        }

        // Reload the parent collection or folder to ensure proper ordering
        const collectionEl = requestEl.closest('.collection-item');
        if (collectionEl) {
          const collectionId = collectionEl.getAttribute('data-id');
          if (collectionId) {
            window.loadCollection(collectionId);
          }
        } else {
          const folderEl = requestEl.closest('.folder-item');
          if (folderEl) {
            const folderId = folderEl.getAttribute('data-id');
            if (folderId) {
              window.loadFolder(folderId);
            }
          }
        }
      } else {
        console.error(data.message || 'Failed to duplicate request');
      }
    })
    .catch(err => {
      console.error('Error duplicating request:', err);
      console.error('Failed to duplicate request. Please try again.');
    });
  };

  // Delete request
  window.deleteRequest = function(requestId) {
    // Remove confirmation dialog and proceed directly

    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`${API_URL}/requests/${requestId}`, {
      method: 'DELETE',
      headers: {
        'x-auth-token': token
      }
    })
    .then(res => res.json())
    .then(data => {
      if (data.message === 'Request deleted') {
        console.log('Request deleted successfully:', data);

        // Remove the request from the UI
        const requestEl = document.querySelector(`.request-item[data-id="${requestId}"]`);
        if (requestEl) {
          // Add deleted class for animation
          requestEl.classList.add('deleted');

          // Remove after animation completes
          setTimeout(() => {
            requestEl.remove();
          }, 300);

          // If the request is currently loaded, clear the form
          const saveBtn = document.getElementById('save-btn');
          if (saveBtn && saveBtn.getAttribute('data-request-id') === requestId.toString()) {
            if (window.clearRequestForm) {
              window.clearRequestForm();
            }
          }
        }
      } else {
        console.error(data.message || 'Failed to delete request');
      }
    })
    .catch(err => {
      console.error('Error deleting request:', err);
      console.error('Failed to delete request. Please try again.');
    });
  };

  // Add request to folder
  window.addRequestToFolder = function(folderId) {
    console.log('Adding request to folder:', folderId);

    // Use the clearRequestForm function to reset the form
    if (window.clearRequestForm) {
      window.clearRequestForm();
    }

    // Set folder ID for the save button
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      // Set folder ID and remove collection ID
      saveBtn.setAttribute('data-folder-id', folderId);
      saveBtn.removeAttribute('data-collection-id');
    }

    // Show requests container and hide auth container
    const authContainer = document.getElementById('auth-container');
    const requestsContainer = document.getElementById('requests-container');

    if (authContainer) {
      authContainer.classList.add('hidden');
    }

    if (requestsContainer) {
      requestsContainer.classList.remove('hidden');
    }

    // Activate params tab by default
    const paramsTabBtn = document.querySelector('.tab-btn[data-tab="params"]');
    const paramsTab = document.getElementById('params-tab');

    if (paramsTabBtn) {
      paramsTabBtn.classList.add('active');
    }

    if (paramsTab) {
      paramsTab.classList.add('active');
    }
  };
});
