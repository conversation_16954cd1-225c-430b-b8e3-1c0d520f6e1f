:root {
    --primary-color: #ff6c37;
    --secondary-color: #66b1df;
    --dark-color: #2c2c2c;
    --light-color: #f4f4f4;
    --danger-color: #dc3545;
    --success-color: #28a745;
  }

  /* Keyframe animations */
  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
  }

  .app {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
  }

  .logo {
    font-size: 1.5rem;
    font-weight: bold;
  }

  .user-area {
    display: flex;
    align-items: center;
  }

  .username {
    margin-right: 15px;
  }

  .btn {
    background-color: white;
    color: var(--primary-color);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-weight: bold;
    transition: all 0.2s ease;
  }

  .btn:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
  }

  /* Special styling for save button */
  #save-btn {
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #save-btn:hover {
    background-color: #218838;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  #save-btn:active {
    transform: scale(0.95);
  }

  .btn-small {
    background-color: transparent;
    border: 1px solid #ccc;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
  }

  .btn-small:hover {
    background-color: #f0f0f0;
  }

  .main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
  }

  /* Sidebar tabs */
  .sidebar-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
  }

  .sidebar-tab {
    flex: 1;
    padding: 10px;
    text-align: center;
    background-color: #f8f9fa;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #666;
  }

  .sidebar-tab:hover {
    background-color: #f0f0f0;
  }

  .sidebar-tab.active {
    background-color: #fff;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
  }

  .sidebar-content {
    display: none;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .sidebar-content.active {
    display: flex;
  }

  .sidebar-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .workspace-selector {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .workspace-selector label {
    font-weight: 500;
    color: #666;
  }

  .workspace-selector select {
    flex: 1;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
  }

  .collections-list, .history-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }

  /* History styles */
  .history-item {
    display: flex;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    align-items: center;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .history-item:hover {
    background-color: #e9ecef;
  }

  .history-item.deleting {
    opacity: 0.7;
  }

  .history-item.deleted {
    opacity: 0;
    margin-top: -8px;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    border-color: transparent;
    height: 0 !important;
  }

  .history-item-method {
    font-weight: bold;
    margin-right: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    min-width: 45px;
    text-align: center;
  }

  .history-item-method.get {
    background-color: #61affe;
    color: white;
  }

  .history-item-method.post {
    background-color: #49cc90;
    color: white;
  }

  .history-item-method.put {
    background-color: #fca130;
    color: white;
  }

  .history-item-method.delete {
    background-color: #f93e3e;
    color: white;
  }

  .history-item-method.patch {
    background-color: #50e3c2;
    color: white;
  }

  .history-item-details {
    flex: 1;
    overflow: hidden;
  }

  .history-item-url {
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .history-item-time {
    font-size: 0.8rem;
    color: #666;
  }

  .history-item-time .request-id {
    color: #007bff;
    font-weight: bold;
  }

  .history-item-actions {
    display: none;
    margin-left: 10px;
  }

  .history-item:hover .history-item-actions {
    display: flex;
  }

  .btn-icon {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    color: #6c757d;
    transition: all 0.2s ease;
  }

  .btn-icon:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
  }

  .delete-history-btn {
    font-size: 0.9rem;
  }

  .history-heading {
    font-weight: bold;
    padding: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
  }

  .history-item-status {
    font-weight: bold;
    margin-right: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    min-width: 30px;
    text-align: center;
  }

  .history-item-status.success {
    background-color: var(--success-color);
    color: white;
  }

  .history-item-status.error {
    background-color: var(--danger-color);
    color: white;
  }

  .loading-message, .error-message, .empty-history-message, .success-message {
    padding: 15px;
    text-align: center;
    color: #666;
    margin-bottom: 10px;
  }

  .error-message {
    color: var(--danger-color);
    font-weight: bold;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 4px;
    padding: 15px;
  }

  .success-message {
    color: var(--success-color);
    font-weight: bold;
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 4px;
    padding: 15px;
  }

  .history-list .btn {
    display: block;
    margin: 10px auto;
    background-color: #6c757d;
    color: white;
  }

  .history-list .btn:hover {
    background-color: #5a6268;
  }

  .history-list .debug-btn {
    background-color: #17a2b8;
  }

  .history-list .debug-btn:hover {
    background-color: #138496;
  }

  .history-list .test-btn {
    background-color: #28a745;
  }

  .history-list .test-btn:hover {
    background-color: #218838;
  }

  .collection-item {
    margin-bottom: 10px;
    border-radius: 4px;
  }

  .collection-header {
    padding: 10px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .collection-header:hover {
    background-color: #f0f0f0;
  }

  .collection-item.active > .collection-header {
    background-color: #e6e6e6;
  }

  .collection-info {
    flex: 1;
  }

  .collection-name {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .collection-description {
    font-size: 0.9rem;
    color: #666;
  }

  .collection-actions, .folder-actions {
    display: none;
  }

  .collection-header:hover .collection-actions,
  .folder-header:hover .folder-actions {
    display: flex;
    gap: 5px;
  }

  .collection-content, .folder-content {
    margin-left: 15px;
    border-left: 1px solid #e0e0e0;
    padding-left: 10px;
  }

  .folder-item {
    margin-bottom: 5px;
    border-radius: 4px;
  }

  .folder-header {
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .folder-header:hover {
    background-color: #f0f0f0;
  }

  .folder-item.active > .folder-header {
    background-color: #e6e6e6;
  }

  .folder-icon {
    margin-right: 8px;
    color: #ffc107;
  }

  .folder-name {
    flex: 1;
    font-weight: 500;
  }

  .folder-content {
    display: none;
  }

  .folder-item.expanded > .folder-content {
    display: block;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .auth-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
  }

  .requests-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* Set height distribution for request and response sections */
  .request-editor {
    flex: 0.6; /* Takes 60% of the available height */
    min-height: 200px;
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #e0e0e0;
    overflow: hidden;
  }

  .response-viewer {
    flex: 0.4; /* Takes 40% of the available height */
    min-height: 200px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .request-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
  }

  .request-name {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .request-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  #view-history-btn {
    background-color: #6c757d;
    color: white;
  }

  #view-history-btn:hover {
    background-color: #5a6268;
  }

  .request-method {
    width: 100px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
  }

  .request-url {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .request-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .tabs {
    display: flex;
    background-color: #f0f0f0;
    border-bottom: 1px solid #e0e0e0;
  }

  .tab-btn {
    background-color: transparent;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    outline: none;
  }

  .tab-btn.active {
    background-color: white;
    border-bottom: 2px solid var(--primary-color);
  }

  .tab-content {
    display: none;
    flex: 1;
    padding: 15px;
    overflow-y: auto;
  }

  .tab-content.active {
    display: block;
  }

  /* Specific styles for response tab content */
  #response-body-tab.tab-content,
  #response-headers-tab.tab-content {
    max-height: 300px;
    overflow-y: auto;
  }

  /* Response headers table */
  .response-headers-table {
    width: 100%;
    border-collapse: collapse;
  }

  .response-headers-table tbody {
    max-height: 250px;
    overflow-y: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  table th, table td {
    padding: 8px;
    text-align: left;
  }

  input[type="text"], input[type="email"], input[type="password"], textarea, select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .raw-body {
    width: 100%;
    height: 200px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-family: monospace;
    resize: vertical;
  }

  /* Response viewer styles moved to the unified section above */

  .response-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
  }

  .status-code {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
  }

  .status-code.success {
    background-color: var(--success-color);
    color: white;
  }

  .status-code.error {
    background-color: var(--danger-color);
    color: white;
  }

  .response-time {
    margin-left: 10px;
    color: #666;
  }

  .response-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevent overflow from affecting layout */
    min-height: 150px; /* Ensure minimum height */
  }

  .response-body-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-height: 300px; /* Set a maximum height */
    overflow-y: auto; /* Enable vertical scrolling */
  }

  .hidden {
    display: none;
  }

  /* Modal styles */
  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
  }

  .modal.visible {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  .modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    width: 50%;
    max-width: 500px;
  }

  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
  }

  .close:hover {
    color: black;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
  }

  /* Authorization styles */
  .auth-type-selector {
    margin-bottom: 20px;
  }

  .auth-type-selector label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .auth-type {
    width: 100%;
    max-width: 300px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .auth-form {
    display: none;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .auth-form.active {
    display: block;
  }

  .auth-input {
    width: 100%;
    max-width: 400px;
  }

  /* Request item styles */
  .request-item {
    display: flex;
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    overflow: hidden;
    align-items: center;
  }

  .request-item:hover {
    background-color: #e9ecef;
  }

  .request-item.deleted {
    opacity: 0;
    margin-top: -5px;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    border-color: transparent;
    height: 0 !important;
  }

  .request-item-method {
    font-weight: bold;
    margin-right: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    background-color: #e0e0e0;
    color: #333;
    min-width: 45px;
    text-align: center;
  }

  .request-item-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .request-item-actions {
    display: none;
    margin-left: 10px;
    gap: 5px;
  }

  .request-item:hover .request-item-actions {
    display: flex;
  }

  .requests-list {
    margin-top: 5px;
    margin-bottom: 10px;
  }