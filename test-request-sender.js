const axios = require('axios');

// Test the /api/send/direct endpoint with request_id functionality
async function testRequestSender() {
  const baseUrl = 'http://localhost:3000'; // Adjust port if needed
  
  console.log('Testing /api/send/direct endpoint with request_id functionality...\n');

  try {
    // Test 1: Send request without request_id (should create new request)
    console.log('Test 1: Sending request without request_id (should create new request)');
    const test1Response = await axios.post(`${baseUrl}/api/send/direct`, {
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      name: 'Test Request 1',
      user_id: 1,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Test 1 Response:', {
      success: test1Response.data.success,
      message: test1Response.data.message,
      status: test1Response.status
    });
    console.log('');

    // Test 2: Send request with non-existent request_id (should create new request)
    console.log('Test 2: Sending request with non-existent request_id (should create new request)');
    const test2Response = await axios.post(`${baseUrl}/api/send/direct`, {
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/2',
      name: 'Test Request 2',
      user_id: 1,
      request_id: 99999, // Non-existent ID
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Test 2 Response:', {
      success: test2Response.data.success,
      message: test2Response.data.message,
      status: test2Response.status
    });
    console.log('');

    // Test 3: Send request with existing request_id (should update existing request)
    // First, let's create a request to get a valid ID
    console.log('Test 3: Creating a request first, then updating it with request_id');
    
    // Create a request using the test endpoint
    const createResponse = await axios.post(`${baseUrl}/api/test/create-request`, {
      name: 'Test Request for Update',
      url: 'https://jsonplaceholder.typicode.com/posts/3',
      method: 'GET',
      collection_id: 1
    });

    if (createResponse.data.success) {
      const requestId = createResponse.data.data.id;
      console.log('Created request with ID:', requestId);

      // Now update it using the send/direct endpoint
      const test3Response = await axios.post(`${baseUrl}/api/send/direct`, {
        method: 'POST',
        url: 'https://jsonplaceholder.typicode.com/posts',
        name: 'Updated Test Request',
        user_id: 1,
        request_id: requestId, // Use the existing ID
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          type: 'raw',
          content: '{"title": "Updated Post", "body": "Updated content", "userId": 1}',
          rawType: 'json'
        }
      });

      console.log('Test 3 Response:', {
        success: test3Response.data.success,
        message: test3Response.data.message,
        status: test3Response.status
      });
    } else {
      console.log('Failed to create request for Test 3:', createResponse.data.message);
    }

    console.log('\nAll tests completed successfully!');

  } catch (error) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testRequestSender();
}

module.exports = testRequestSender;
