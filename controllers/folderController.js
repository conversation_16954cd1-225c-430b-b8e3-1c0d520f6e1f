const Folder = require('../models/Folder');
const Collection = require('../models/Collection');
const Request = require('../models/Request');
const { sendResponse } = require('../utils/responseUtil');

exports.getFolders = async (req, res) => {
  console.log('Getting folders with query:', req.query);
  try {
    const { collection_id, parent_folder_id } = req.query;

    let folders;
    if (parent_folder_id) {
      // Get folders within a parent folder
      folders = await Folder.findByParentFolderId(parent_folder_id);

      // Check if user has access to the parent folder
      const parentFolder = await Folder.findById(parent_folder_id);
      if (!parentFolder) {
        return sendResponse(res, false, 'Parent folder not found', null, 404);
      }

      // If parent folder belongs to a collection, check user access
      if (parentFolder.collection_Id) {
        const collection = await Collection.findById(parentFolder.collection_Id);
        if (collection.login_user_Id !== req.user.id) {
          return sendResponse(res, false, 'Not authorized', null, 403);
        }
      }
    } else if (collection_id) {
      // Get top-level folders in a collection
      folders = await Folder.findByCollectionId(collection_id);

      // Check if user has access to the collection
      const collection = await Collection.findById(collection_id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
    } else {
      return sendResponse(res, false, 'Either collection_id or parent_folder_id is required', null, 400);
    }

    // Transform folders to match the expected format
    const transformedFolders = folders.map(folder => ({
      id: folder.collection_folder_Id,
      name: folder.collection_folder_Name,
      description: folder.collection_folder_Description,
      collection_id: folder.collection_Id,
      parent_folder_id: folder.collection_folder_Parent_Id,
      order: folder.collection_folder_Order,
      is_active: folder.is_active,
      created_at: folder.created_at,
      updated_at: folder.updated_at
    }));

    sendResponse(res, true, 'Folders retrieved successfully', transformedFolders);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.getFolder = async (req, res) => {
  console.log('Getting folder with ID:', req.params.id);
  try {
    const folder = await Folder.findById(req.params.id);

    if (!folder) {
      return sendResponse(res, false, 'Folder not found', null, 404);
    }

    // Check if user has access to the collection
    if (folder.collection_Id) {
      const collection = await Collection.findById(folder.collection_Id);
      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
    } else if (folder.collection_folder_Parent_Id) {
      // Check parent folder access recursively
      let currentFolderId = folder.collection_folder_Parent_Id;
      let hasAccess = false;

      while (currentFolderId && !hasAccess) {
        const parentFolder = await Folder.findById(currentFolderId);
        if (!parentFolder) break;

        if (parentFolder.collection_Id) {
          const collection = await Collection.findById(parentFolder.collection_Id);
          hasAccess = collection.login_user_Id === req.user.id;
          break;
        }

        currentFolderId = parentFolder.collection_folder_Parent_Id;
      }

      if (!hasAccess) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
    }

    // Get child folders
    const childFolders = await Folder.findByParentFolderId(folder.collection_folder_Id);

    // Get requests in this folder
    const requests = await Request.findByFolderId(folder.collection_folder_Id);

    // Transform folder to match the expected format
    const transformedFolder = {
      id: folder.collection_folder_Id,
      name: folder.collection_folder_Name,
      description: folder.collection_folder_Description,
      collection_id: folder.collection_Id,
      parent_folder_id: folder.collection_folder_Parent_Id,
      order: folder.collection_folder_Order,
      is_active: folder.is_active,
      created_at: folder.created_at,
      updated_at: folder.updated_at
    };

    // Transform child folders
    const transformedChildFolders = childFolders.map(childFolder => ({
      id: childFolder.collection_folder_Id,
      name: childFolder.collection_folder_Name,
      description: childFolder.collection_folder_Description,
      collection_id: childFolder.collection_Id,
      parent_folder_id: childFolder.collection_folder_Parent_Id,
      order: childFolder.collection_folder_Order,
      is_active: childFolder.is_active,
      created_at: childFolder.created_at,
      updated_at: childFolder.updated_at
    }));

    // Transform requests with safe JSON parsing
    const transformedRequests = requests.map(request => {
      // Safe JSON parsing function
      const safeJsonParse = (jsonString, defaultValue = {}) => {
        if (!jsonString) return defaultValue;
        try {
          return typeof jsonString === 'object' ? jsonString : JSON.parse(jsonString);
        } catch (error) {
          console.error('Error parsing JSON:', error, 'Value:', jsonString);
          return defaultValue;
        }
      };

      return {
        id: request.api_request_Id,
        name: request.api_request_Name,
        url: request.api_request_Url,
        method: request.api_request_Method,
        headers: safeJsonParse(request.api_request_Headers, {}),
        body: safeJsonParse(request.api_request_Body, {}),
        params: safeJsonParse(request.api_request_Query_Params, {}),
        auth_type: request.api_request_Auth_Type,
        collection_id: folder.collection_Id,
        folder_id: folder.collection_folder_Id,
        is_active: request.is_active,
        created_at: request.created_at,
        updated_at: request.updated_at
      };
    });

    // Combine all properties into a single folder object
    // This matches the structure expected by the frontend
    const folderResponse = {
      ...transformedFolder,
      folders: transformedChildFolders,
      requests: transformedRequests
    };

    console.log('Sending folder response:', folderResponse);

    sendResponse(res, true, 'Folder retrieved successfully', folderResponse);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.createFolder = async (req, res) => {
  try {
    const { name, description, collection_id, parent_folder_id, order } = req.body;

    if (!collection_id && !parent_folder_id) {
      return sendResponse(res, false, 'Either collection_id or parent_folder_id is required', null, 400);
    }

    // Variables to store the actual collection_id to use
    let actualCollectionId = collection_id;
    let parentFolder = null;

    // Check if user has access to the collection or parent folder
    if (collection_id) {
      const collection = await Collection.findById(collection_id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
    }
    else if (parent_folder_id) {
      parentFolder = await Folder.findById(parent_folder_id);
      if (!parentFolder) {
        return sendResponse(res, false, 'Parent folder not found', null, 404);
      }

      console.log('Parent folder found:', parentFolder);
      console.log('Parent folder collection_Id:', parentFolder.collection_Id);
      // Get the collection ID from the parent folder
      if (parentFolder.collection_Id) {
        actualCollectionId = parentFolder.collection_Id;
        console.log('Using collection ID from parent folder:', actualCollectionId);

        // Check if user has access to the parent folder's collection
        const collection = await Collection.findById(actualCollectionId);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        if (collection.login_user_Id !== req.user.id) {
          return sendResponse(res, false, 'Not authorized', null, 403);
        }
      } else {
        console.log('inside else part...');
        // Recursively check parent folders to find a collection ID
        // let currentFolderId = parentFolder.collection_folder_Parent_Id;
        // let hasAccess = false;

        // while (currentFolderId && !hasAccess) {
        //   const grandParentFolder = await Folder.findById(currentFolderId);
        //   if (!grandParentFolder) break;

        //   if (grandParentFolder.collection_Id) {
        //     actualCollectionId = grandParentFolder.collection_Id;
        //     console.log('Using collection ID from grandparent folder:', actualCollectionId);

        //     const collection = await Collection.findById(actualCollectionId);
        //     hasAccess = collection && collection.login_user_Id === req.user.id;
        //     break;
        //   }

        //   currentFolderId = grandParentFolder.collection_folder_Parent_Id;
        // }

        // if (!hasAccess) {
        //   return sendResponse(res, false, 'Not authorized', null, 403);
        // }
      }
    }

    // Use NULL for top-level folders (no parent)
    // If parent_folder_id is 0 or falsy, explicitly set it to NULL
    const finalParentFolderId = parent_folder_id && parent_folder_id !== 0 ? parent_folder_id : null;
    console.log('Using parent folder ID:', finalParentFolderId, 'instead of', parent_folder_id, 'actualCollectionId :', actualCollectionId);

    // Make sure we have a valid collection_id
    if (!actualCollectionId && finalParentFolderId) {
      // If we have a parent folder but no collection_id, try to get the collection_id from the parent folder
      try {
        const parentFolder = await Folder.findById(finalParentFolderId);
        if (parentFolder && parentFolder.collection_Id) {
          actualCollectionId = parentFolder.collection_Id;
          console.log('Using collection ID from parent folder:', actualCollectionId);
        }
      } catch (error) {
        console.error('Error getting collection ID from parent folder:', error);
      }
    }

    // Ensure we have a collection_id
    if (!actualCollectionId) {
      return sendResponse(res, false, 'Collection ID is required', null, 400);
    }

    console.log('Creating folder with data:', {
      name,
      description,
      collection_id: actualCollectionId,
      parent_folder_id: finalParentFolderId,
      order
    });

    const folder = await Folder.create({
      name,
      description,
      collection_id: actualCollectionId,
      parent_folder_id: finalParentFolderId,
      order
    });

    // Transform the response to match the expected format
    const transformedFolder = {
      id: folder.id,
      name: folder.name,
      description: folder.description,
      collection_id: actualCollectionId,
      parent_folder_id: finalParentFolderId,
      order: folder.order,
      is_active: folder.is_active,
      is_delete: folder.is_delete
    };

    sendResponse(res, true, 'Folder created successfully', transformedFolder, 201);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.updateFolder = async (req, res) => {
  try {
    console.log('Updating folder with ID:', req.params.id);
    console.log('Update data:', req.body);

    const { name, description, order } = req.body;

    // Check if folder exists
    const folder = await Folder.findById(req.params.id);
    if (!folder) {
      console.log('Folder not found:', req.params.id);
      return sendResponse(res, false, 'Folder not found', null, 404);
    }

    console.log('Found folder to update:', folder);

    // Check if user has access to the folder
    let hasAccess = false;

    if (folder.collection_Id) {
      const collection = await Collection.findById(folder.collection_Id);
      if (collection) {
        hasAccess = collection.login_user_Id === req.user.id;
        console.log('Checking access via collection:', {
          collectionId: folder.collection_Id,
          collectionUserId: collection.login_user_Id,
          requestUserId: req.user.id,
          hasAccess
        });
      } else {
        console.log('Collection not found:', folder.collection_Id);
      }
    } else if (folder.collection_folder_Parent_Id) {
      // Recursively check parent folders
      let currentFolderId = folder.collection_folder_Parent_Id;
      console.log('Checking access via parent folder:', currentFolderId);

      while (currentFolderId && !hasAccess) {
        const parentFolder = await Folder.findById(currentFolderId);
        if (!parentFolder) {
          console.log('Parent folder not found:', currentFolderId);
          break;
        }

        if (parentFolder.collection_Id) {
          const collection = await Collection.findById(parentFolder.collection_Id);
          if (collection) {
            hasAccess = collection.login_user_Id === req.user.id;
            console.log('Found parent folder collection:', {
              collectionId: parentFolder.collection_Id,
              collectionUserId: collection.login_user_Id,
              requestUserId: req.user.id,
              hasAccess
            });
            break;
          } else {
            console.log('Parent folder collection not found:', parentFolder.collection_Id);
            break;
          }
        }

        currentFolderId = parentFolder.collection_folder_Parent_Id;
      }
    }

    if (!hasAccess) {
      console.log('User not authorized to update folder');
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    console.log('User authorized to update folder, proceeding with update');
    const updatedFolder = await Folder.update(req.params.id, { name, description, order });
    console.log('Folder updated successfully:', updatedFolder);

    // Transform the response to match the expected format
    const transformedFolder = {
      id: updatedFolder.id,
      name: updatedFolder.name,
      description: updatedFolder.description,
      collection_id: updatedFolder.collection_id,
      parent_folder_id: updatedFolder.parent_folder_id,
      order: updatedFolder.order,
      is_active: updatedFolder.is_active,
      is_delete: updatedFolder.is_delete
    };

    console.log('Sending response with transformed folder:', transformedFolder);
    sendResponse(res, true, 'Folder updated successfully', transformedFolder);
  } catch (error) {
    console.error('Error updating folder:', error);
    sendResponse(res, false, error.message || 'Server error', null, 500);
  }
};

exports.deleteFolder = async (req, res) => {
  try {
    // Get folder ID from request params (for DELETE) or body (for POST)
    const folderId = req.params.id || req.body.folder_id;

    // Validate folder ID
    if (!folderId) {
      return sendResponse(res, false, 'Folder ID is required', null, 400);
    }

    console.log('Deleting folder with ID:', folderId);

    // Check if folder exists
    const folder = await Folder.findById(folderId);
    if (!folder) {
      return sendResponse(res, false, 'Folder not found', null, 404);
    }

    // Check if user has access to the folder
    let hasAccess = false;

    if (folder.collection_Id) {
      const collection = await Collection.findById(folder.collection_Id);
      hasAccess = collection.login_user_Id === req.user.id;
    } else if (folder.collection_folder_Parent_Id) {
      // Recursively check parent folders
      let currentFolderId = folder.collection_folder_Parent_Id;

      while (currentFolderId && !hasAccess) {
        const parentFolder = await Folder.findById(currentFolderId);
        if (!parentFolder) break;

        if (parentFolder.collection_Id) {
          const collection = await Collection.findById(parentFolder.collection_Id);
          hasAccess = collection.login_user_Id === req.user.id;
          break;
        }

        currentFolderId = parentFolder.collection_folder_Parent_Id;
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    const result = await Folder.delete(folderId);

    sendResponse(res, true, 'Folder deleted successfully', result);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};
