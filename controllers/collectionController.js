const Collection = require('../models/Collection');
const Request = require('../models/Request');
const Folder = require('../models/Folder');
const { sendResponse } = require('../utils/responseUtil');

exports.getCollections = async (req, res) => {
  try {
    // Check if the request is GET or POST and get workspace_id accordingly
    let workspaceId;
    let workspaceIdSource;

    if (req.method === 'POST') {
      // Get workspace_id from request body
      workspaceId = req.body.workspace_id ? req.body.workspace_id : 123;
      workspaceIdSource = req.body.workspace_id;
    } else {
      // Get workspace_id from query params (for backward compatibility)
      workspaceId = req.query.workspace_id ? req.query.workspace_id : 123;
      workspaceIdSource = req.query.workspace_id;
    }

    // Convert to integer if it's a number
    if (workspaceId !== 'all') {
      workspaceId = parseInt(workspaceId);
    }

    // If workspace_id is 'all', get collections from all workspaces
    let collections;
    if (workspaceIdSource === 'all') {
      collections = await Collection.findByUserIdAllWorkspaces(req.user.id);
    } else {
      collections = await Collection.findByUserId(req.user.id, workspaceId);
    }

    // Transform the response to match the expected format
    const transformedCollections = collections.map(collection => ({
      id: collection.collection_Id,
      name: collection.collection_Title,
      description: collection.collection_Description,
      user_id: collection.login_user_Id,
      workspace_id: collection.cms_workspace_Id,
      is_active: collection.is_active,
      created_at: collection.created_at,
      updated_at: collection.updated_at
    }));

    sendResponse(res, true, 'Collections retrieved successfully', transformedCollections);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.getCollection = async (req, res) => {
  try {
    const collection = await Collection.findById(req.params.id);

    if (!collection) {
      return sendResponse(res, false, 'Collection not found', null, 404);
    }

    if (collection.login_user_Id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Get top-level requests (not in any folder)
    const requests = await Request.findByCollectionId(collection.collection_Id);

    // Get top-level folders
    const folders = await Folder.findByCollectionId(collection.collection_Id);

    // For each folder, get its child folders and requests
    for (let i = 0; i < folders.length; i++) {
      folders[i].folders = await Folder.getChildFoldersWithRequests(folders[i].collection_folder_Id);
      folders[i].requests = await Folder.getRequestsInFolder(folders[i].collection_folder_Id);
    }

    // Safe JSON parsing function
    const safeJsonParse = (jsonString, defaultValue = {}) => {
      if (!jsonString) return defaultValue;
      try {
        return typeof jsonString === 'object' ? jsonString : JSON.parse(jsonString);
      } catch (error) {
        console.error('Error parsing JSON:', error, 'Value:', jsonString);
        return defaultValue;
      }
    };

    // Transform the collection to match the expected format
    const transformedCollection = {
      id: collection.collection_Id,
      name: collection.collection_Title,
      description: collection.collection_Description,
      user_id: collection.login_user_Id,
      workspace_id: collection.cms_workspace_Id,
      is_active: collection.is_active,
      created_at: collection.created_at,
      updated_at: collection.updated_at
    };

    // Transform requests to match the expected format with safe JSON parsing
    const transformedRequests = requests.map(request => {

      return {
        id: request.api_request_Id,
        name: request.api_request_Name,
        url: request.api_request_Url,
        method: request.api_request_Method,
        headers: safeJsonParse(request.api_request_Headers, {}),
        body: safeJsonParse(request.api_request_Body, {}),
        params: safeJsonParse(request.api_request_Query_Params, {}),
        auth_type: request.api_request_Auth_Type,
        collection_id: collection.collection_Id,
        folder_id: null,
        is_active: request.is_active,
        created_at: request.created_at,
        updated_at: request.updated_at
      };
    });

    // Transform folders to match the expected format
    const transformedFolders = folders.map(folder => ({
      id: folder.collection_folder_Id,
      name: folder.collection_folder_Name,
      description: folder.collection_folder_Description,
      collection_id: folder.collection_Id,
      parent_folder_id: folder.collection_folder_Parent_Id,
      order: folder.collection_folder_Order,
      is_active: folder.is_active,
      created_at: folder.created_at,
      updated_at: folder.updated_at,
      folders: folder.folders ? folder.folders.map(subfolder => ({
        id: subfolder.collection_folder_Id,
        name: subfolder.collection_folder_Name,
        description: subfolder.collection_folder_Description,
        collection_id: subfolder.collection_Id,
        parent_folder_id: subfolder.collection_folder_Parent_Id,
        order: subfolder.collection_folder_Order,
        is_active: subfolder.is_active,
        created_at: subfolder.created_at,
        updated_at: subfolder.updated_at,
        folders: subfolder.folders || [],
        requests: subfolder.requests ? subfolder.requests.map(req => ({
          id: req.api_request_Id,
          name: req.api_request_Name,
          url: req.api_request_Url,
          method: req.api_request_Method,
          headers: safeJsonParse(req.api_request_Headers, {}),
          body: safeJsonParse(req.api_request_Body, {}),
          params: safeJsonParse(req.api_request_Query_Params, {}),
          auth_type: req.api_request_Auth_Type,
          collection_id: collection.collection_Id,
          folder_id: subfolder.collection_folder_Id,
          is_active: req.is_active,
          created_at: req.created_at,
          updated_at: req.updated_at
        })) : []
      })) : [],
      requests: folder.requests ? folder.requests.map(req => ({
        id: req.api_request_Id,
        name: req.api_request_Name,
        url: req.api_request_Url,
        method: req.api_request_Method,
        headers: safeJsonParse(req.api_request_Headers, {}),
        body: safeJsonParse(req.api_request_Body, {}),
        params: safeJsonParse(req.api_request_Query_Params, {}),
        auth_type: req.api_request_Auth_Type,
        collection_id: collection.collection_Id,
        folder_id: folder.collection_folder_Id,
        is_active: req.is_active,
        created_at: req.created_at,
        updated_at: req.updated_at
      })) : []
    }));

    const collectionData = {
      ...transformedCollection,
      requests: transformedRequests,
      folders: transformedFolders
    };

    sendResponse(res, true, 'Collection retrieved successfully', collectionData);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.createCollection = async (req, res) => {
  try {
    const { name, description, workspace_id } = req.body;
    console.log('req.user.id:', req.user.id , 'req.body:', req.body);
    if (!name || name.trim() === '') {
      return sendResponse(res, false, 'Collection name is required', null, 400);
    }

    // Use workspace_id from request body or default to 123
    const workspaceId = workspace_id ? parseInt(workspace_id) : 123;

    const collection = await Collection.create({
      title: name,
      description,
      user_id: req.user.id,
      workspace_id: workspaceId
    });

    // Transform the response to match the expected format
    const transformedCollection = {
      id: collection.id,
      name: collection.title,
      description: collection.description,
      user_id: collection.user_id,
      workspace_id: collection.workspace_id,
      is_active: collection.is_active
    };

    sendResponse(res, true, 'Collection created successfully', transformedCollection, 201);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.updateCollection = async (req, res) => {
  try {
    const { name, description, workspace_id } = req.body;

    if (!name || name.trim() === '') {
      return sendResponse(res, false, 'Collection name is required', null, 400);
    }

    // Check if collection exists
    const collection = await Collection.findById(req.params.id);
    if (!collection) {
      return sendResponse(res, false, 'Collection not found', null, 404);
    }

    // Check if user owns the collection
    if (collection.login_user_Id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Parse workspace_id if provided
    const workspaceId = workspace_id ? parseInt(workspace_id) : undefined;

    // Update collection with or without workspace_id
    const updatedCollection = await Collection.update(req.params.id, {
      title: name,
      description,
      workspace_id: workspaceId
    });

    // Transform the response to match the expected format
    const transformedCollection = {
      id: updatedCollection.id,
      name: updatedCollection.title,
      description: updatedCollection.description,
      user_id: req.user.id,
      workspace_id: updatedCollection.workspace_id,
      is_active: updatedCollection.is_active
    };

    sendResponse(res, true, 'Collection updated successfully', transformedCollection);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.deleteCollection = async (req, res) => {
  try {
    // Check if collection exists
    const collection = await Collection.findById(req.params.id);
    if (!collection) {
      return sendResponse(res, false, 'Collection not found', null, 404);
    }

    // Check if user owns the collection
    if (collection.login_user_Id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Soft delete the collection (set is_delete to 1)
    await Collection.delete(req.params.id);

    sendResponse(res, true, 'Collection deleted successfully', {
      id: req.params.id,
      is_delete: 1
    });
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};