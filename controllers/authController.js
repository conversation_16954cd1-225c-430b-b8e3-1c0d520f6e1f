const User = require('../models/User');
const jwt = require('jsonwebtoken');
const { sendResponse } = require('../utils/responseUtil');

exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return sendResponse(res, false, 'User already exists', null, 400);
    }

    // Create new user
    const user = await User.create({ name, email, password });

    // Generate JWT token
    const token = jwt.sign({ id: user.id }, process.env.JWT_SECRET || 'your-secret-key', {
      expiresIn: '1d'
    });

    sendResponse(res, true, 'User registered successfully', {
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email
      }
    }, 201);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await User.findByEmail(email);
    if (!user) {
      return sendResponse(res, false, 'User not found', null, 404);
    }

    // Verify password
    const isMatch = await User.verifyPassword(password, user.password);
    if (!isMatch) {
      return sendResponse(res, false, 'Invalid credentials', null, 400);
    }

    // Generate JWT token
    const token = jwt.sign({ id: user.id }, process.env.JWT_SECRET || 'your-secret-key', {
      expiresIn: '1d'
    });

    sendResponse(res, true, 'Login successful', {
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return sendResponse(res, false, 'User not found', null, 404);
    }

    sendResponse(res, true, 'User profile retrieved successfully', {
      id: user.id,
      name: user.name,
      email: user.email
    });
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};