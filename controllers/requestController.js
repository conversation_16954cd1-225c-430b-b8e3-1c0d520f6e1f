const Request = require('../models/Request');
const Response = require('../models/Response');
const Collection = require('../models/Collection');
const CollectionRequestMapping = require('../models/CollectionRequestMapping');
const axios = require('axios');
const { sendResponse } = require('../utils/responseUtil');

exports.createRequest = async (req, res) => {
  console.log('createRequest controller called with path:', req.path);
  console.log('Request body:', JSON.stringify(req.body).substring(0, 200) + '...');
  try {
    const { name, url, method, headers, body, params, auth_type, collection_id, folder_id, order } = req.body;

    // Check if we have either collection_id or folder_id
    if (!collection_id && !folder_id) {
      return sendResponse(res, false, 'Either collection_id or folder_id is required', null, 400);
    }

    let effectiveCollectionId = collection_id;

    // If we have a collection_id, check if it exists and belongs to the user
    if (collection_id) {
      const collection = await Collection.findById(collection_id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
    }

    // If we have a folder_id, check if it exists and belongs to the user
    if (folder_id) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(folder_id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      // Check if folder belongs to a collection that belongs to the user
      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (collection.login_user_Id !== req.user.id) {
          return sendResponse(res, false, 'Not authorized', null, 403);
        }

        // If collection_id wasn't provided, use the folder's collection_id
        if (!effectiveCollectionId) {
          effectiveCollectionId = folder.collection_Id;
        }
      } else {
        // Folder belongs to another folder, recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;
        let hasAccess = false;
        let foundCollectionId = null;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            hasAccess = collection.login_user_Id === req.user.id;
            foundCollectionId = parentFolder.collection_Id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }

        if (!hasAccess) {
          return sendResponse(res, false, 'Not authorized', null, 403);
        }

        // If collection_id wasn't provided, use the found collection_id
        if (!effectiveCollectionId && foundCollectionId) {
          effectiveCollectionId = foundCollectionId;
        }
      }
    }

    // Create the request
    const request = await Request.create({
      name,
      url,
      method,
      headers,
      body,
      params,
      auth_type,
      workspace_id: 123, // Default workspace ID
      user_id: req.user.id,
      collection_id: effectiveCollectionId,
      folder_id,
      order
    });

    // Transform the response to match the expected format
    const transformedRequest = {
      id: request.id,
      name: request.name,
      url: request.url,
      method: request.method,
      headers: request.headers,
      body: request.body,
      params: request.params,
      auth_type: request.auth_type,
      collection_id: request.collection_id,
      folder_id: request.folder_id,
      order: request.order,
      is_active: request.is_active,
      is_delete: request.is_delete
    };

    sendResponse(res, true, 'Request created successfully', transformedRequest, 201);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error: ' + error.message, null, 500);
  }
};

exports.getRequest = async (req, res) => {
  try {
    const request = await Request.findById(req.params.id);

    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection or folder
    let hasAccess = false;

    if (mapping.collection_Id) {
      const collection = await Collection.findById(mapping.collection_Id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }

      hasAccess = true;
    }

    if (mapping.collection_folder_Id && !hasAccess) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(mapping.collection_folder_Id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        hasAccess = collection.login_user_Id === req.user.id;
      } else {
        // Recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            if (!collection) break;

            hasAccess = collection.login_user_Id === req.user.id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Safe JSON parsing function
    const safeJsonParse = (jsonString, defaultValue = {}) => {
      if (!jsonString) return defaultValue;
      try {
        return typeof jsonString === 'object' ? jsonString : JSON.parse(jsonString);
      } catch (error) {
        console.error('Error parsing JSON:', error, 'Value:', jsonString);
        return defaultValue;
      }
    };

    // Transform the request to match the expected format
    const transformedRequest = {
      id: request.api_request_Id,
      name: request.api_request_Name,
      url: request.api_request_Url,
      method: request.api_request_Method,
      headers: safeJsonParse(request.api_request_Headers, {}),
      body: safeJsonParse(request.api_request_Body, {}),
      params: safeJsonParse(request.api_request_Query_Params, {}),
      auth_type: request.api_request_Auth_Type,
      collection_id: mapping.collection_Id,
      folder_id: mapping.collection_folder_Id,
      order: mapping.collection_request_mapping_Order,
      workspace_id: request.cms_workspace_Id,
      user_id: request.login_user_Id,
      is_active: request.is_active,
      is_delete: request.is_delete,
      created_at: request.created_at,
      updated_at: request.updated_at
    };

    // First, try to get the response from the history table
    const History = require('../models/History');
    let historyEntries = [];
    let responseData = null;

    try {
      // Get the most recent history entry for this request
      historyEntries = await History.findByRequestId(
        request.api_request_Id,
        req.user.id,
        request.cms_workspace_Id || 123,
        1 // Limit to the most recent entry
      );

      console.log(`Found ${historyEntries.length} history entries for request ${request.api_request_Id}`);

      if (historyEntries.length > 0) {
        const historyEntry = historyEntries[0];
        console.log('Using history entry:', historyEntry.api_request_history_Id);

        // Extract response data from the history entry
        if (historyEntry.response) {
          // The response field should contain both request and response data
          if (historyEntry.response.response) {
            responseData = {
              id: historyEntry.api_request_history_Id,
              request_id: request.api_request_Id,
              status_code: historyEntry.api_request_history_Status_Code ||
                          (historyEntry.response.response && historyEntry.response.response.status_code) || 0,
              headers: (historyEntry.response.response && historyEntry.response.response.headers) || {},
              body: (historyEntry.response.response && historyEntry.response.response.body) || {},
              response_time: historyEntry.api_request_history_Response_Time ||
                            (historyEntry.response.response && historyEntry.response.response.response_time) || 0,
              created_at: historyEntry.created_at,
              source: 'history'
            };
            console.log('Found response data in history entry');
          }
        }
      }
    } catch (historyError) {
      console.error('Error fetching history entries:', historyError);
      // Continue with trying to get response from responses table
    }

    // If no response data found in history, try the responses table
    if (!responseData) {
      console.log('No response data found in history, checking responses table');
      let responses = [];
      try {
        responses = await Response.findByRequestId(request.api_request_Id);
        console.log(`Found ${responses.length} responses in responses table`);

        if (responses.length > 0) {
          // Transform the most recent response
          const response = responses[0];
          responseData = {
            id: response.id,
            request_id: response.request_id,
            status_code: response.status_code || 0,
            headers: safeJsonParse(response.headers, {}),
            body: safeJsonParse(response.body, {}),
            response_time: response.response_time || 0,
            created_at: response.created_at,
            source: 'responses'
          };
          console.log('Found response data in responses table');
        }
      } catch (responseError) {
        console.error('Error fetching responses:', responseError);
        // Continue with null responseData
      }
    }

    // Add the response data to the request if available
    if (responseData) {
      transformedRequest.lastResponse = responseData;
      console.log('Added response data to request');
    }

    sendResponse(res, true, 'Request retrieved successfully', transformedRequest);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.updateRequest = async (req, res) => {
  try {
    const { name, url, method, headers, body, params, auth_type, collection_id, folder_id, order } = req.body;

    // Check if request exists
    const request = await Request.findById(req.params.id);
    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection
    let hasAccess = false;

    if (mapping.collection_Id) {
      const collection = await Collection.findById(mapping.collection_Id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }
      hasAccess = true;
    }

    if (mapping.collection_folder_Id && !hasAccess) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(mapping.collection_folder_Id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        hasAccess = collection.login_user_Id === req.user.id;
      } else {
        // Recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            if (!collection) break;

            hasAccess = collection.login_user_Id === req.user.id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    const updatedRequest = await Request.update(req.params.id, {
      name,
      url,
      method,
      headers,
      body,
      params,
      auth_type,
      collection_id: collection_id || mapping.collection_Id,
      folder_id: folder_id || mapping.collection_folder_Id,
      order: order !== undefined ? order : mapping.collection_request_mapping_Order
    });

    console.log('Request updated successfully:', updatedRequest);

    // The Request.update method now returns a properly formatted object
    // No need for additional transformation
    const transformedRequest = updatedRequest;

    sendResponse(res, true, 'Request updated successfully', transformedRequest);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.deleteRequest = async (req, res) => {
  try {
    // Check if request exists
    const request = await Request.findById(req.params.id);
    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection or folder
    let hasAccess = false;

    if (mapping.collection_Id) {
      const collection = await Collection.findById(mapping.collection_Id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }

      hasAccess = true;
    }

    if (mapping.collection_folder_Id && !hasAccess) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(mapping.collection_folder_Id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        hasAccess = collection.login_user_Id === req.user.id;
      } else {
        // Recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            if (!collection) break;

            hasAccess = collection.login_user_Id === req.user.id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Soft delete the request (set is_delete to 1)
    const result = await Request.delete(req.params.id);

    sendResponse(res, true, 'Request deleted successfully', {
      id: req.params.id,
      is_delete: 1
    });
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

// Rename a request
exports.renameRequest = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name || name.trim() === '') {
      return sendResponse(res, false, 'Name is required', null, 400);
    }

    // Check if request exists
    const request = await Request.findById(req.params.id);
    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection or folder
    let hasAccess = false;

    if (mapping.collection_Id) {
      const collection = await Collection.findById(mapping.collection_Id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }

      hasAccess = true;
    }

    if (mapping.collection_folder_Id && !hasAccess) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(mapping.collection_folder_Id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        hasAccess = collection.login_user_Id === req.user.id;
      } else {
        // Recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            if (!collection) break;

            hasAccess = collection.login_user_Id === req.user.id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Rename the request
    const updatedRequest = await Request.rename(req.params.id, name);

    console.log('Request renamed successfully:', updatedRequest);

    // The Request.rename method now returns a properly formatted object
    // No need for additional transformation
    const transformedRequest = updatedRequest;

    sendResponse(res, true, 'Request renamed successfully', transformedRequest);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

// Duplicate a request
exports.duplicateRequest = async (req, res) => {
  try {
    // Check if request exists
    const request = await Request.findById(req.params.id);
    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection or folder
    let hasAccess = false;

    if (mapping.collection_Id) {
      const collection = await Collection.findById(mapping.collection_Id);
      if (!collection) {
        return sendResponse(res, false, 'Collection not found', null, 404);
      }

      if (collection.login_user_Id !== req.user.id) {
        return sendResponse(res, false, 'Not authorized', null, 403);
      }

      hasAccess = true;
    }

    if (mapping.collection_folder_Id && !hasAccess) {
      const Folder = require('../models/Folder');
      const folder = await Folder.findById(mapping.collection_folder_Id);

      if (!folder) {
        return sendResponse(res, false, 'Folder not found', null, 404);
      }

      if (folder.collection_Id) {
        const collection = await Collection.findById(folder.collection_Id);
        if (!collection) {
          return sendResponse(res, false, 'Collection not found', null, 404);
        }

        hasAccess = collection.login_user_Id === req.user.id;
      } else {
        // Recursively check parent folders
        let currentFolderId = folder.collection_folder_Parent_Id;

        while (currentFolderId && !hasAccess) {
          const parentFolder = await Folder.findById(currentFolderId);
          if (!parentFolder) break;

          if (parentFolder.collection_Id) {
            const collection = await Collection.findById(parentFolder.collection_Id);
            if (!collection) break;

            hasAccess = collection.login_user_Id === req.user.id;
            break;
          }

          currentFolderId = parentFolder.collection_folder_Parent_Id;
        }
      }
    }

    if (!hasAccess) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    // Duplicate the request
    const duplicatedRequest = await Request.duplicate(req.params.id);

    console.log('Request duplicated successfully:', duplicatedRequest);

    // The Request.duplicate method now returns a properly formatted object
    // No need for additional transformation
    const transformedRequest = duplicatedRequest;

    sendResponse(res, true, 'Request duplicated successfully', transformedRequest, 201);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

exports.executeRequest = async (req, res) => {
  try {
    // Check if request exists
    const request = await Request.findById(req.params.id);
    if (!request) {
      return sendResponse(res, false, 'Request not found', null, 404);
    }

    // Get the mapping to find collection and folder
    const mapping = await CollectionRequestMapping.findByRequestId(request.api_request_Id);

    if (!mapping) {
      return sendResponse(res, false, 'Request mapping not found', null, 404);
    }

    // Check if user has access to the collection
    const collection = await Collection.findById(mapping.collection_Id);
    if (!collection) {
      return sendResponse(res, false, 'Collection not found', null, 404);
    }

    if (collection.login_user_Id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    const startTime = new Date().getTime();

    // Parse headers
    const headers = JSON.parse(request.api_request_Headers || '{}');

    // Parse body
    const body = JSON.parse(request.api_request_Body || '{}');

    // Parse params (query parameters)
    const params = JSON.parse(request.api_request_Query_Params || '{}');

    // Handle auth based on auth_type
    const auth_type = request.api_request_Auth_Type;

    // Add authorization headers based on auth type
    if (auth_type) {
      switch (auth_type) {
        case 'basic':
          if (params.username && params.password) {
            const base64Credentials = Buffer.from(`${params.username}:${params.password}`).toString('base64');
            headers['Authorization'] = `Basic ${base64Credentials}`;
          }
          break;

        case 'bearer':
          if (params.token) {
            headers['Authorization'] = `Bearer ${params.token}`;
          }
          break;

        case 'api-key':
          if (params.key && params.value && params.in === 'header') {
            headers[params.key] = params.value;
          }
          break;
      }
    }

    // Prepare URL with API key if needed
    let url = request.api_request_Url;
    if (auth_type === 'api-key' && params.key && params.value && params.in === 'query') {
      const urlObj = new URL(url.includes('://') ? url : `http://${url}`);
      urlObj.searchParams.append(params.key, params.value);
      url = urlObj.toString();
    }

    // Execute the request
    let response;
    try {
      response = await axios({
        method: request.api_request_Method,
        url: url,
        headers,
        data: request.api_request_Method !== 'GET' ? body : undefined,
        validateStatus: () => true, // Don't throw on non-2xx status
      });
    } catch (error) {
      response = {
        status: error.code || 500,
        headers: {},
        data: { error: error.message }
      };
    }

    const endTime = new Date().getTime();
    const responseTime = endTime - startTime;

    // Save the response
    const savedResponse = await Response.create({
      request_id: request.api_request_Id,
      status_code: response.status,
      headers: response.headers,
      body: response.data,
      response_time: responseTime
    });

    // Save to history
    const History = require('../models/History');
    await History.create({
      workspace_id: request.cms_workspace_Id,
      user_id: req.user.id,
      request_id: request.api_request_Id,
      response: {
        status: response.status,
        headers: response.headers || {},
        body: response.data || {},
      },
      status_code: response.status,
      response_time: responseTime
    });

    const responseData = {
      status: response.status,
      headers: response.headers,
      body: response.data,
      responseTime,
      id: savedResponse.id
    };

    sendResponse(res, true, 'Request executed successfully', responseData);
  } catch (error) {
    console.error(error);
    sendResponse(res, false, 'Server error', null, 500);
  }
};