const axios = require('axios');
const { sendResponse } = require('../utils/responseUtil');
const History = require('../models/History');

// Helper function to safely stringify objects with circular references
function safeStringify(obj) {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }
    return value;
  });
}

// Send an API request and save to history
exports.sendRequest = async (req, res) => {
  try {
    console.log('Sending request with data:', {
      method: req.body.method,
      url: req.body.url,
      name: req.body.name || req.body.url
    });

    // Get user ID from request or body
    let userId;
    if (req.user && req.user.id) {
      // User ID from token
      userId = req.user.id;
      console.log('User ID from token:', userId);
    } else if (req.body.user_id) {
      // User ID from request body
      userId = typeof req.body.user_id === 'string' ? parseInt(req.body.user_id) : req.body.user_id;
      console.log('User ID from request body:', userId);
    } else {
      // Default user ID
      userId = 1;
      console.warn('No user ID found in request, using default user ID:', userId);
    }

    // Prepare request config
    const config = {
      method: req.body.method || 'GET',
      url: req.body.url,
      headers: req.body.headers || {},
      timeout: 30000 // 30 seconds timeout
    };

    // Add request body if present
    if (req.body.body && Object.keys(req.body.body).length > 0) {
      if (req.body.body.type === 'raw' && req.body.body.content) {
        // Handle raw body
        if (req.body.body.rawType === 'json') {
          try {
            config.data = JSON.parse(req.body.body.content);
          } catch (e) {
            config.data = req.body.body.content;
          }
        } else {
          config.data = req.body.body.content;
        }
      } else if (req.body.body.type === 'form-data' && req.body.body.content) {
        // Handle form data
        const formData = new FormData();
        Object.entries(req.body.body.content).forEach(([key, value]) => {
          formData.append(key, value);
        });
        config.data = formData;
      } else if (req.body.body.type === 'x-www-form-urlencoded' && req.body.body.content) {
        // Handle URL encoded form data
        const params = new URLSearchParams();
        Object.entries(req.body.body.content).forEach(([key, value]) => {
          params.append(key, value);
        });
        config.data = params;
      } else {
        // Handle other body types
        config.data = req.body.body;
      }
    }

    // Add URL parameters if present
    if (req.body.params && Object.keys(req.body.params).length > 0) {
      const url = new URL(config.url);
      Object.entries(req.body.params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
      config.url = url.toString();
    }

    console.log('Sending request with config:', {
      method: config.method,
      url: config.url,
      headers: config.headers
    });

    // Record start time
    const startTime = Date.now();

    // Send the request
    const response = await axios(config);

    // Record end time and calculate response time
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // Prepare response data
    const responseData = {
      status_code: response.status,
      headers: response.headers,
      body: response.data,
      response_time: responseTime
    };

    // Prepare history data
    const historyData = {
      user_id: userId,
      method: config.method,
      url: config.url,
      name: req.body.name || config.url,
      headers: JSON.parse(safeStringify(config.headers || {})),
      body: req.body.body || {},
      params: req.body.params || {},
      auth: req.body.auth || null,
      status_code: response.status,
      response_headers: JSON.parse(safeStringify(response.headers || {})),
      response_body: response.data,
      response_time: responseTime
    };

    // Save to history
    try {
      const history = await History.create(historyData);
      console.log('Request saved to history with ID:', history.id);
    } catch (historyError) {
      console.error('Error saving request to history:', historyError);
      // Continue even if history saving fails
    }

    // Send response
    sendResponse(res, true, 'Request sent successfully!!!', {
      request: {
        method: config.method,
        url: config.url,
        headers: config.headers,
        body: req.body.body
      },
      response: responseData
    });
    //sendResponse(res, true, 'Request sent successfully', responseData);
  } catch (error) {
    console.error('Error sending request:', error);
    
    // Handle axios errors
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      sendResponse(res, false, 'Request failed with status: ' + error.response.status, {
        status_code: error.response.status,
        headers: error.response.headers,
        body: error.response.data,
        error: error.message
      }, 200); // Still return 200 to client so they can see the error response
    } else if (error.request) {
      // The request was made but no response was received
      sendResponse(res, false, 'No response received from server', {
        error: error.message,
        request: error.request
      }, 500);
    } else {
      // Something happened in setting up the request that triggered an Error
      sendResponse(res, false, 'Error setting up request', {
        error: error.message
      }, 500);
    }
  }
};
