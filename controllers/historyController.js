const History = require('../models/History');
const { sendResponse } = require('../utils/responseUtil');

// Get history for the authenticated user (POST version)
exports.getHistory = async (req, res) => {
  try {
    // Get user ID from authenticated user or request body
    const userId = req.body.user_id ? parseInt(req.body.user_id) : req.user.id;

    // Parse request body parameters
    const workspaceId = parseInt(req.body.workspace_id) || 123; // Default workspace ID if not provided
    const limit = parseInt(req.body.limit) || 50; // Default limit if not provided
    const page = parseInt(req.body.page) || 1; // Default page if not provided
    const offset = (page - 1) * limit; // Calculate offset for pagination

    console.log('Getting history with parameters:', {
      userId,
      workspaceId,
      limit,
      page,
      offset
    });

    // Validate parameters
    if (isNaN(userId) || userId <= 0) {
      return sendResponse(res, false, 'Invalid user ID', null, 400);
    }

    if (isNaN(workspaceId) || workspaceId <= 0) {
      return sendResponse(res, false, 'Invalid workspace ID', null, 400);
    }

    if (isNaN(limit) || limit <= 0 || limit > 100) {
      return sendResponse(res, false, 'Invalid limit (must be between 1 and 100)', null, 400);
    }

    if (isNaN(page) || page <= 0) {
      return sendResponse(res, false, 'Invalid page number', null, 400);
    }

    // Check if the authenticated user is allowed to access this user's history
    // Only allow if it's the same user or if the authenticated user is an admin
    // if (req.user && req.user.id !== userId && req.user.role !== 'admin') {
    //   console.warn(`User ${req.user.id} attempted to access history for user ${userId}`);
    //   // For security, don't reveal that the user exists, just return empty results
    //   return sendResponse(res, true, 'History retrieved successfully', {
    //     items: [],
    //     pagination: {
    //       total: 0,
    //       page: page,
    //       limit: limit,
    //       totalPages: 0
    //     }
    //   });
    // }

    // console.log(`Fetching history for user ID: ${userId}, workspace ID: ${workspaceId}, limit: ${limit}, page: ${page}`);

    // // Test database connection
    // const connectionTest = await History.testConnection();
    // console.log('Database connection test:', connectionTest);

    // if (!connectionTest.success) {
    //   return sendResponse(res, false, 'Database connection failed', {
    //     error: connectionTest.error
    //   }, 500);
    // }

    // Get user history with pagination
    const history = await getUserHistory(userId, workspaceId, limit, offset);
    console.log(`Found ${history.items.length} history items for user ID ${userId} (page ${page} of ${history.totalPages})`);

    // Return the history with pagination metadata
    sendResponse(res, true, 'History retrieved successfully', {
      items: history.items,
      pagination: {
        total: history.total,
        page: page,
        limit: limit,
        totalPages: history.totalPages
      }
    });
  } catch (err) {
    console.error('Error getting history:', err);
    sendResponse(res, false, 'Server error', {
      error: err.message
    }, 500);
  }
};

// Helper function to get user history with pagination
async function getUserHistory(userId, workspaceId, limit, offset) {
  try {
    // Ensure the history table exists by testing the connection
    // This will internally call createHistoryTable()
    await History.testConnection();

    // Get database connection
    const db = require('../config/db');

    // Check if the table exists before querying
    const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
    if (tables.length === 0) {
      console.log('api_request_history table does not exist, returning empty array');
      return { items: [], total: 0, totalPages: 0 };
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM api_request_history h
      WHERE h.login_user_Id = ? AND h.cms_workspace_Id = ? AND h.is_active = 1 AND h.is_delete = 0
    `;

    const [countResult] = await db.query(countQuery, [userId, workspaceId]);
    const total = countResult[0].total;

    // If no history items found, return empty result
    if (total === 0) {
      console.log(`No history items found for user ID ${userId} and workspace ID ${workspaceId}`);
      return { items: [], total: 0, totalPages: 0 };
    }

    const totalPages = Math.ceil(total / limit);

    // Get paginated history items
    const query = `
      SELECT h.*, r.api_request_Name, r.api_request_Url, r.api_request_Method
      FROM api_request_history h
      LEFT JOIN api_request r ON h.api_request_Id = r.api_request_Id
      WHERE h.login_user_Id = ? AND h.cms_workspace_Id = ? AND h.is_active = 1 AND h.is_delete = 0
      ORDER BY h.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [results] = await db.query(query, [userId, workspaceId, limit, offset]);

    // Parse JSON fields
    const items = results.map(item => {
      return {
        ...item,
        response: parseJsonField(item.api_request_history_Response)
      };
    });

    return { items, total, totalPages };
  } catch (error) {
    console.error('Error in getUserHistory:', error);
    throw error;
  }
}

// Helper function to safely parse JSON fields (copied from History model)
function parseJsonField(field) {
  if (!field) return {};

  // If field is already an object, return it
  if (typeof field === 'object' && field !== null) return field;

  // Try to parse the field as JSON
  try {
    return JSON.parse(field);
  } catch (error) {
    console.error('Error parsing JSON field:', error);
    console.log('Field value:', field);
    console.log('Field type:', typeof field);
    return {};
  }
}

// Get history for a specific request
exports.getHistoryByRequestId = async (req, res) => {
  try {
    const requestId = req.params.requestId;

    // Check if the request exists and belongs to the user
    const history = await History.findByRequestId(requestId, req.user.id);

    if (!history || history.length === 0) {
      return sendResponse(res, false, 'No history found for this request', null, 404);
    }

    sendResponse(res, true, 'Request history retrieved successfully', history);
  } catch (err) {
    console.error(err);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

// Add a request to history
exports.addToHistory = async (req, res) => {
  try {
    console.log('addToHistory controller called with path:', req.path);
    console.log('Request body:', JSON.stringify(req.body).substring(0, 200) + '...');

    // Log authentication status
    if (req.user) {
      console.log('Request is authenticated, user ID:', req.user.id);
    } else {
      console.log('Request is NOT authenticated, using body data');
    }

    // Ensure request_id is an integer if provided
    let requestId = null;
    if (req.body.request_id) {
      requestId = parseInt(req.body.request_id);
      if (isNaN(requestId)) {
        console.log('Invalid request_id, setting to null');
        requestId = null;
      } else {
        console.log('Valid request_id:', requestId);
      }
    }

    // Get user ID from request or body
    let userId;

    if (req.user && req.user.id) {
      // User ID from token
      userId = req.user.id;
      console.log('User ID from token:', userId, 'Type:', typeof userId);
    } else if (req.body.user_id) {
      // User ID from request body
      userId = typeof req.body.user_id === 'string' ? parseInt(req.body.user_id) : req.body.user_id;
      console.log('User ID from request body:', userId, 'Type:', typeof userId);
    } else {
      // Default user ID
      userId = 1;
      console.warn('No user ID found in request, using default user ID:', userId);
    }

    // Validate user ID
    if (isNaN(userId)) {
      console.error('Invalid user ID:', userId);
      userId = 1; // Default to user ID 1 if invalid
    }

    console.log('Adding to history with request data:', {
      user_id: userId,
      request_id: requestId,
      method: req.body.method,
      url: req.body.url,
      name: req.body.name || req.body.url
    });

    const historyData = {
      user_id: userId,
      workspace_id: 123, // Default workspace ID
      request_id: requestId,
      method: req.body.method,
      url: req.body.url,
      name: req.body.name || req.body.url,
      headers: req.body.headers || {},
      body: req.body.body || {},
      params: req.body.params || {},
      auth: req.body.auth || null,
      status_code: req.body.status_code || 0,
      response_headers: req.body.response_headers || {},
      response_body: req.body.response_body || {},
      response_time: req.body.response_time || 0
    };

    const history = await History.create(historyData);
    console.log('History created with ID:', history.id, 'and request_id:', history.request_id);
    sendResponse(res, true, 'History entry created successfully', history, 201);
  } catch (err) {
    console.error('Error adding to history:', err);
    sendResponse(res, false, 'Server error', { error: err.message }, 500);
  }
};

// Get a single history entry
exports.getHistoryById = async (req, res) => {
  try {
    const history = await History.findById(req.params.id);

    if (!history) {
      return sendResponse(res, false, 'History entry not found', null, 404);
    }

    // Check if user has access to this history entry
    if (history.user_id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    sendResponse(res, true, 'History entry retrieved successfully', history);
  } catch (err) {
    console.error(err);
    sendResponse(res, false, 'Server error', null, 500);
  }
};

// Delete a history entry (soft delete)
exports.deleteHistory = async (req, res) => {
  try {
    const history = await History.findById(req.params.id);

    if (!history) {
      return sendResponse(res, false, 'History entry not found', null, 404);
    }

    // Check if user has access to this history entry
    if (history.user_id !== req.user.id) {
      return sendResponse(res, false, 'Not authorized', null, 403);
    }

    await History.delete(req.params.id);
    sendResponse(res, true, 'History entry deleted successfully', {
      id: req.params.id,
      is_active: 0
    });
  } catch (err) {
    console.error('Error deleting history entry:', err);
    sendResponse(res, false, 'Server error', {
      error: err.message
    }, 500);
  }
};

// Clear all history for the authenticated user (soft delete)
exports.clearHistory = async (req, res) => {
  try {
    await History.clearByUserId(req.user.id);
    sendResponse(res, true, 'History cleared successfully', {
      userId: req.user.id
    });
  } catch (err) {
    console.error('Error clearing history:', err);
    sendResponse(res, false, 'Server error', {
      error: err.message
    }, 500);
  }
};
