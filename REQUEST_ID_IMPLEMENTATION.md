# Request ID Implementation for /api/send/direct Endpoint

## Overview
This document describes the implementation of the `request_id` parameter functionality for the `/api/send/direct` endpoint. The endpoint now checks if a `request_id` exists in the `api_request` table and either updates the existing request or creates a new one.

## Changes Made

### 1. Modified `controllers/requestSenderController.js`

#### Added Request Model Import
```javascript
const Request = require('../models/Request');
```

#### Added Request ID Handling Logic
- **Extract `request_id`** from `req.body.request_id`
- **Check if request exists** using `Request.findById()`
- **If exists**: Update the existing request with new data using `Request.update()`
- **If not exists**: Create a new request using `Request.create()`
- **Pass `request_id`** to history creation for proper tracking

#### Key Implementation Details
```javascript
// Handle request_id parameter - check if it exists in api_request table
let requestId = null;
if (req.body.request_id) {
  try {
    const existingRequest = await Request.findById(req.body.request_id);
    if (existingRequest) {
      console.log('Found existing request with ID:', req.body.request_id);
      requestId = req.body.request_id;
      
      // Update the existing request with new data
      const updateData = {
        name: req.body.name || existingRequest.api_request_Name,
        url: req.body.url || existingRequest.api_request_Url,
        method: req.body.method || existingRequest.api_request_Method,
        headers: req.body.headers || {},
        body: req.body.body || {},
        params: req.body.params || {},
        auth_type: req.body.auth?.type || existingRequest.api_request_Auth_Type
      };
      
      await Request.update(req.body.request_id, updateData);
      console.log('Updated existing request with ID:', req.body.request_id);
    } else {
      console.log('Request ID not found, will create new request');
    }
  } catch (error) {
    console.error('Error checking/updating existing request:', error);
    // Continue with creating new request
  }
}

// If request_id doesn't exist or wasn't provided, create a new request
if (!requestId) {
  try {
    const newRequestData = {
      name: req.body.name || req.body.url || 'Unnamed Request',
      url: req.body.url || '',
      method: req.body.method || 'GET',
      headers: req.body.headers || {},
      body: req.body.body || {},
      params: req.body.params || {},
      auth_type: req.body.auth?.type || null,
      workspace_id: req.body.workspace_id || 123,
      user_id: userId,
      collection_id: req.body.collection_id || 1, // Default collection
      folder_id: req.body.folder_id || null
    };

    const newRequest = await Request.create(newRequestData);
    requestId = newRequest.id;
    console.log('Created new request with ID:', requestId);
  } catch (error) {
    console.error('Error creating new request:', error);
    // Continue without request_id
  }
}
```

#### Updated History Data
```javascript
// Prepare history data
const historyData = {
  user_id: userId,
  request_id: requestId, // Include the request_id for history tracking
  method: config.method,
  url: config.url,
  name: req.body.name || config.url,
  headers: JSON.parse(safeStringify(config.headers || {})),
  body: req.body.body || {},
  params: req.body.params || {},
  auth: req.body.auth || null,
  status_code: response.status,
  response_headers: JSON.parse(safeStringify(response.headers || {})),
  response_body: response.data,
  response_time: responseTime
};
```

### 2. Fixed `models/Request.js`

#### Added Safe JSON Parse Helper
```javascript
// Helper function to safely parse JSON fields
function safeJsonParse(jsonString, defaultValue = {}) {
  if (!jsonString) return defaultValue;
  try {
    return typeof jsonString === 'object' ? jsonString : JSON.parse(jsonString);
  } catch (error) {
    console.error('Error parsing JSON:', error, 'Value:', jsonString);
    return defaultValue;
  }
}
```

#### Fixed Unused Variables
- Removed unused `request` variable in the `update` method
- Fixed the `rename` method to use the correct `safeJsonParse` function

## API Usage

### Request Format
```javascript
POST /api/send/direct
Content-Type: application/json

{
  "request_id": 123,           // Optional: ID of existing request to update
  "method": "POST",            // HTTP method
  "url": "https://api.example.com/data",
  "name": "My API Request",    // Optional: Request name
  "user_id": 1,               // Optional: User ID
  "workspace_id": 123,        // Optional: Workspace ID (defaults to 123)
  "collection_id": 1,         // Optional: Collection ID (defaults to 1)
  "folder_id": null,          // Optional: Folder ID
  "headers": {                // Optional: Request headers
    "Content-Type": "application/json",
    "Authorization": "Bearer token"
  },
  "body": {                   // Optional: Request body
    "type": "raw",
    "content": "{\"key\": \"value\"}",
    "rawType": "json"
  },
  "params": {                 // Optional: Query parameters
    "param1": "value1"
  },
  "auth": {                   // Optional: Authentication
    "type": "bearer"
  }
}
```

### Behavior
1. **If `request_id` is provided and exists**: Updates the existing request in the `api_request` table
2. **If `request_id` is provided but doesn't exist**: Creates a new request
3. **If `request_id` is not provided**: Creates a new request
4. **Always**: Sends the HTTP request and saves the response to history with the appropriate `request_id`

## Testing

A test file `test-request-sender.js` has been created to verify the functionality:

```bash
node test-request-sender.js
```

The test covers:
1. Sending request without `request_id` (creates new request)
2. Sending request with non-existent `request_id` (creates new request)
3. Sending request with existing `request_id` (updates existing request)

## Database Schema

The implementation works with the existing database schema:

### `api_request` table
- `api_request_Id` (Primary Key)
- `cms_workspace_Id`
- `login_user_Id`
- `api_request_Name`
- `api_request_Url`
- `api_request_Method`
- `api_request_Headers` (JSON)
- `api_request_Body` (JSON)
- `api_request_Query_Params` (JSON)
- `api_request_Auth_Type`
- `is_active`
- `is_delete`
- `created_at`
- `updated_at`

### `api_request_history` table
- `api_request_history_Id` (Primary Key)
- `cms_workspace_Id`
- `login_user_Id`
- `api_request_Id` (Foreign Key, can be NULL)
- `api_request_history_Response` (JSON)
- `api_request_history_Status_Code`
- `api_request_history_Response_Time`
- `is_active`
- `is_delete`
- `created_at`
- `updated_at`

## Error Handling

The implementation includes comprehensive error handling:
- If checking for existing request fails, it continues with creating a new request
- If updating existing request fails, it logs the error and continues
- If creating new request fails, it continues without a `request_id`
- The HTTP request sending and history saving continue regardless of request creation/update status

## Backward Compatibility

The implementation is fully backward compatible:
- Existing API calls without `request_id` continue to work as before
- All existing functionality remains unchanged
- No breaking changes to the API contract
