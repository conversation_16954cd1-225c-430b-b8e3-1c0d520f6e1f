const db = require('../config/db');

class CollectionRequestMapping {
  static async findByCollectionId(collectionId) {
    const [rows] = await db.query(
      'SELECT * FROM collection_request_mapping WHERE collection_Id = ? AND is_active = 1 AND is_delete = 0',
      [collectionId]
    );
    return rows;
  }

  static async findByFolderId(folderId) {
    const [rows] = await db.query(
      'SELECT * FROM collection_request_mapping WHERE collection_folder_Id = ? AND is_active = 1 AND is_delete = 0',
      [folderId]
    );
    return rows;
  }

  static async findByRequestId(requestId) {
    const [rows] = await db.query(
      'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
      [requestId]
    );
    return rows[0];
  }

  static async findById(id) {
    const [rows] = await db.query(
      'SELECT * FROM collection_request_mapping WHERE collection_request_mapping_Id = ? AND is_active = 1 AND is_delete = 0',
      [id]
    );
    return rows[0];
  }

  static async create(mappingData) {
    const { collection_id, folder_id, request_id, order = null } = mappingData;

    const [result] = await db.query(
      'INSERT INTO collection_request_mapping (collection_Id, collection_folder_Id, api_request_Id, collection_request_mapping_Order, is_active, is_delete) VALUES (?, ?, ?, ?, 1, 0)',
      [collection_id, folder_id, request_id, order]
    );

    return {
      id: result.insertId,
      collection_id,
      folder_id,
      request_id,
      order,
      is_active: 1,
      is_delete: 0
    };
  }

  static async update(id, mappingData) {
    const { collection_id, folder_id, order } = mappingData;

    let query = 'UPDATE collection_request_mapping SET';
    const params = [];
    const updateFields = [];

    if (collection_id !== undefined) {
      updateFields.push(' collection_Id = ?');
      params.push(collection_id);
    }

    if (folder_id !== undefined) {
      updateFields.push(' collection_folder_Id = ?');
      params.push(folder_id);
    }

    if (order !== undefined) {
      updateFields.push(' collection_request_mapping_Order = ?');
      params.push(order);
    }

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    query += updateFields.join(',');
    query += ' WHERE collection_request_mapping_Id = ? AND is_active = 1 AND is_delete = 0';
    params.push(id);

    await db.query(query, params);

    return {
      id,
      ...(collection_id !== undefined && { collection_id }),
      ...(folder_id !== undefined && { folder_id }),
      ...(order !== undefined && { order })
    };
  }

  static async delete(id) {
    // Soft delete - update is_delete to 1
    await db.query(
      'UPDATE collection_request_mapping SET is_delete = 1 WHERE collection_request_mapping_Id = ?',
      [id]
    );
    return { id, is_delete: 1 };
  }

  static async hardDelete(id) {
    // Hard delete - actually remove from database (for admin use only)
    await db.query('DELETE FROM collection_request_mapping WHERE collection_request_mapping_Id = ?', [id]);
    return { id };
  }

  static async moveRequest(requestId, newCollectionId, newFolderId, newOrder = null) {
    // Get the current mapping
    const [mappings] = await db.query(
      'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
      [requestId]
    );

    if (mappings.length === 0) {
      throw new Error('Request mapping not found');
    }

    const mapping = mappings[0];

    // Update the mapping
    await db.query(
      'UPDATE collection_request_mapping SET collection_Id = ?, collection_folder_Id = ?, collection_request_mapping_Order = ? WHERE collection_request_mapping_Id = ?',
      [newCollectionId, newFolderId, newOrder, mapping.collection_request_mapping_Id]
    );

    return {
      id: mapping.collection_request_mapping_Id,
      collection_id: newCollectionId,
      folder_id: newFolderId,
      request_id: requestId,
      order: newOrder
    };
  }

  static async reorderRequests(folderId, requestIds) {
    // Begin transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Update the order of each request
      for (let i = 0; i < requestIds.length; i++) {
        await connection.query(
          'UPDATE collection_request_mapping SET collection_request_mapping_Order = ? WHERE api_request_Id = ? AND collection_folder_Id = ? AND is_active = 1 AND is_delete = 0',
          [i, requestIds[i], folderId]
        );
      }

      // Commit transaction
      await connection.commit();
      connection.release();

      return { success: true, message: 'Requests reordered successfully' };
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      connection.release();
      throw error;
    }
  }
}

module.exports = CollectionRequestMapping;
