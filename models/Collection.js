const db = require('../config/db');

class Collection {
  static async findByUserId(userId, workspaceId = 123) {
    const [rows] = await db.query(
      'SELECT * FROM collection WHERE login_user_Id = ? AND cms_workspace_Id = ? AND is_active = 1 AND is_delete = 0',
      [userId, workspaceId]
    );
    return rows;
  }

  static async findByUserIdAllWorkspaces(userId) {
    const [rows] = await db.query(
      'SELECT * FROM collection WHERE login_user_Id = ? AND is_active = 1 AND is_delete = 0',
      [userId]
    );
    return rows;
  }

  static async findById(id) {
    const [rows] = await db.query(
      'SELECT * FROM collection WHERE collection_Id = ? AND is_active = 1 AND is_delete = 0',
      [id]
    );
    return rows[0];
  }

  static async findByIdIncludeInactive(id) {
    const [rows] = await db.query('SELECT * FROM collection WHERE collection_Id = ?', [id]);
    return rows[0];
  }

  static async create(collectionData) {
    const { title, description, user_id, workspace_id = 123 } = collectionData;

    const [result] = await db.query(
      'INSERT INTO collection (collection_Title, collection_Description, login_user_Id, cms_workspace_Id, is_active, is_delete) VALUES (?, ?, ?, ?, 1, 0)',
      [title, description, user_id, workspace_id]
    );

    return {
      id: result.insertId,
      title,
      description,
      user_id,
      workspace_id,
      is_active: 1,
      is_delete: 0
    };
  }

  static async update(id, collectionData) {
    const { title, description, workspace_id } = collectionData;

    // If workspace_id is provided, update it as well
    if (workspace_id) {
      await db.query(
        'UPDATE collection SET collection_Title = ?, collection_Description = ?, cms_workspace_Id = ? WHERE collection_Id = ? AND is_active = 1 AND is_delete = 0',
        [title, description, workspace_id, id]
      );

      return {
        id,
        title,
        description,
        workspace_id,
        is_active: 1,
        is_delete: 0
      };
    } else {
      // Otherwise, just update title and description
      await db.query(
        'UPDATE collection SET collection_Title = ?, collection_Description = ? WHERE collection_Id = ? AND is_active = 1 AND is_delete = 0',
        [title, description, id]
      );

      // Get the current workspace_id
      const [rows] = await db.query('SELECT cms_workspace_Id FROM collection WHERE collection_Id = ?', [id]);
      const currentWorkspaceId = rows[0] ? rows[0].cms_workspace_Id : 123;

      return {
        id,
        title,
        description,
        workspace_id: currentWorkspaceId,
        is_active: 1,
        is_delete: 0
      };
    }
  }

  static async delete(id) {
    // Soft delete - update is_delete to 1
    await db.query('UPDATE collection SET is_delete = 1 WHERE collection_Id = ?', [id]);
    return {
      id,
      is_delete: 1
    };
  }

  static async hardDelete(id) {
    // Hard delete - actually remove from database (for admin use only)
    await db.query('DELETE FROM collection WHERE collection_Id = ?', [id]);
    return { id };
  }
}

module.exports = Collection;