const db = require('../config/db');

class Folder {
  static async findByCollectionId(collectionId) {
    const [rows] = await db.query(
      'SELECT * FROM collection_folder WHERE collection_Id = ? AND collection_folder_Parent_Id IS NULL AND is_active = 1 AND is_delete = 0',
      [collectionId]
    );
    console.log(`Found ${rows.length} top-level folders for collection ${collectionId}`);
    return rows;
  }

  static async findByParentFolderId(parentFolderId) {
    console.log('Finding folders by parent folder ID:', parentFolderId);
    const [rows] = await db.query(
      'SELECT * FROM collection_folder WHERE collection_folder_Parent_Id = ? AND is_active = 1 AND is_delete = 0',
      [parentFolderId]
    );
    return rows;
  }

  static async findById(id) {
    const [rows] = await db.query(
      'SELECT * FROM collection_folder WHERE collection_folder_Id = ? AND is_active = 1 AND is_delete = 0',
      [id]
    );
    return rows[0];
  }

  static async create(folderData) {
    const { name, description, collection_id, parent_folder_id, order = null } = folderData;
    // Log the folder data being created
    console.log('Creating folder in model with data:', {
      name,
      description,
      collection_id,
      parent_folder_id,
      order
    });

    // Validate collection_id
    if (!collection_id && !parent_folder_id) {
      throw new Error('Either collection_id or parent_folder_id is required');
    }

    // Use NULL for top-level folders (no parent)
    // If parent_folder_id is 0, explicitly set it to NULL
    const finalParentFolderId = parent_folder_id === 0 ? null : parent_folder_id;
    console.log('Using parent folder ID:', finalParentFolderId, 'instead of', parent_folder_id);

    // Insert the folder into the database
    const [result] = await db.query(
      'INSERT INTO collection_folder (collection_folder_Name, collection_folder_Description, collection_Id, collection_folder_Parent_Id, collection_folder_Order, is_active, is_delete) VALUES (?, ?, ?, ?, ?, 1, 0)',
      [name, description, collection_id, finalParentFolderId, order]
    );

    console.log('Folder created with ID:', result.insertId);

    return {
      id: result.insertId,
      name,
      description,
      collection_id,
      parent_folder_id: finalParentFolderId,
      order,
      is_active: 1,
      is_delete: 0
    };
  }

  static async update(id, folderData) {
    const { name, description, order } = folderData;

    console.log('Updating folder in model with data:', {
      id,
      name,
      description,
      order
    });

    // First, get the current folder data to ensure we have all fields
    const folder = await this.findById(id);
    if (!folder) {
      throw new Error(`Folder with ID ${id} not found`);
    }

    console.log('Current folder data:', folder);

    let query = 'UPDATE collection_folder SET collection_folder_Name = ?, collection_folder_Description = ?';
    let params = [name, description];

    if (order !== undefined) {
      query += ', collection_folder_Order = ?';
      params.push(order);
    }

    query += ' WHERE collection_folder_Id = ? AND is_active = 1 AND is_delete = 0';
    params.push(id);

    console.log('Executing query:', query);
    console.log('With params:', params);

    const [result] = await db.query(query, params);
    console.log('Update result:', result);

    // Return a more complete object with all the folder properties
    return {
      id: parseInt(id),
      name,
      description,
      collection_id: folder.collection_Id,
      parent_folder_id: folder.collection_folder_Parent_Id,
      order: order !== undefined ? order : folder.collection_folder_Order,
      is_active: folder.is_active,
      is_delete: folder.is_delete
    };
  }

  static async delete(id) {
    // First, recursively soft delete all child folders
    const childFolders = await this.findByParentFolderId(id);
    for (const childFolder of childFolders) {
      await this.delete(childFolder.collection_folder_Id);
    }

    // Soft delete the folder
    await db.query('UPDATE collection_folder SET is_delete = 1 WHERE collection_folder_Id = ?', [id]);
    return { id, is_delete: 1 };
  }

  static async hardDelete(id) {
    // First, recursively hard delete all child folders
    const childFolders = await this.findByParentFolderId(id);
    for (const childFolder of childFolders) {
      await this.hardDelete(childFolder.collection_folder_Id);
    }

    // Hard delete the folder
    await db.query('DELETE FROM collection_folder WHERE collection_folder_Id = ?', [id]);
    return { id };
  }

  static async getFullHierarchy(collectionId) {
    // Get top-level folders
    const topFolders = await this.findByCollectionId(collectionId);

    // Recursively get child folders and requests
    for (let i = 0; i < topFolders.length; i++) {
      topFolders[i].folders = await this.getChildFoldersWithRequests(topFolders[i].collection_folder_Id);
      topFolders[i].requests = await this.getRequestsInFolder(topFolders[i].collection_folder_Id);
    }

    return topFolders;
  }

  static async getChildFoldersWithRequests(folderId) {
    const childFolders = await this.findByParentFolderId(folderId);

    for (let i = 0; i < childFolders.length; i++) {
      childFolders[i].folders = await this.getChildFoldersWithRequests(childFolders[i].collection_folder_Id);
      childFolders[i].requests = await this.getRequestsInFolder(childFolders[i].collection_folder_Id);
    }

    return childFolders;
  }

  static async getRequestsInFolder(folderId) {
    // Get requests through the mapping table
    const [rows] = await db.query(`
      SELECT r.*
      FROM api_request r
      JOIN collection_request_mapping m ON r.api_request_Id = m.api_request_Id
      WHERE m.collection_folder_Id = ? AND r.is_active = 1 AND r.is_delete = 0 AND m.is_active = 1 AND m.is_delete = 0
      ORDER BY m.collection_request_mapping_Order ASC
    `, [folderId]);

    return rows;
  }
}

module.exports = Folder;
