const db = require('../config/db');
const fs = require('fs');
const path = require('path');

// Create api_request_history table if it doesn't exist
const createHistoryTable = async () => {
  try {
    // Check if table exists
    const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
    if (tables.length === 0) {
      console.log('Creating api_request_history table...');

      // Create the table
      await db.query(`
        CREATE TABLE IF NOT EXISTS \`api_request_history\` (
          \`api_request_history_Id\` INT NOT NULL AUTO_INCREMENT,
          \`cms_workspace_Id\` INT NULL,
          \`login_user_Id\` INT NOT NULL,
          \`api_request_Id\` INT NULL,
          \`api_request_history_Response\` LONGTEXT NULL,
          \`api_request_history_Status_Code\` INT NULL,
          \`api_request_history_Response_Time\` INT NULL,
          \`is_active\` TINYINT(1) NOT NULL DEFAULT 1,
          \`is_delete\` TINYINT(1) NOT NULL DEFAULT 0,
          \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          \`updated_at\` TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
          \`deleted_at\` TIMESTAMP NULL DEFAULT NULL,
          PRIMARY KEY (\`api_request_history_Id\`),
          INDEX \`login_user_Id_idx\` (\`login_user_Id\` ASC),
          INDEX \`api_request_Id_idx\` (\`api_request_Id\` ASC)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      // Check if the foreign key constraint exists and drop it if it does
      try {
        const [constraints] = await db.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.TABLE_CONSTRAINTS
          WHERE TABLE_NAME = 'api_request_history'
          AND CONSTRAINT_TYPE = 'FOREIGN KEY'
          AND CONSTRAINT_SCHEMA = DATABASE()
        `);

        for (const constraint of constraints) {
          console.log('Dropping constraint:', constraint.CONSTRAINT_NAME);
          await db.query(`ALTER TABLE api_request_history DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}`);
        }
      } catch (error) {
        console.error('Error checking/dropping constraints:', error);
      }

      // Check if api_request_Id is NOT NULL and modify it to allow NULL
      try {
        const [columns] = await db.query(`
          SELECT COLUMN_NAME, IS_NULLABLE
          FROM information_schema.COLUMNS
          WHERE TABLE_NAME = 'api_request_history'
          AND COLUMN_NAME = 'api_request_Id'
          AND TABLE_SCHEMA = DATABASE()
        `);

        if (columns.length > 0 && columns[0].IS_NULLABLE === 'NO') {
          console.log('Modifying api_request_Id to allow NULL values');
          await db.query(`ALTER TABLE api_request_history MODIFY COLUMN api_request_Id INT NULL`);
        }
      } catch (error) {
        console.error('Error checking/modifying column:', error);
      }

      console.log('api_request_history table created successfully');
    } else {
      console.log('api_request_history table already exists, checking constraints and columns...');

      // Check if the foreign key constraint exists and drop it if it does
      try {
        const [constraints] = await db.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.TABLE_CONSTRAINTS
          WHERE TABLE_NAME = 'api_request_history'
          AND CONSTRAINT_TYPE = 'FOREIGN KEY'
          AND CONSTRAINT_SCHEMA = DATABASE()
        `);

        for (const constraint of constraints) {
          console.log('Dropping constraint:', constraint.CONSTRAINT_NAME);
          await db.query(`ALTER TABLE api_request_history DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}`);
        }
      } catch (error) {
        console.error('Error checking/dropping constraints:', error);
      }

      // Check if api_request_Id is NOT NULL and modify it to allow NULL
      try {
        const [columns] = await db.query(`
          SELECT COLUMN_NAME, IS_NULLABLE
          FROM information_schema.COLUMNS
          WHERE TABLE_NAME = 'api_request_history'
          AND COLUMN_NAME = 'api_request_Id'
          AND TABLE_SCHEMA = DATABASE()
        `);

        if (columns.length > 0 && columns[0].IS_NULLABLE === 'NO') {
          console.log('Modifying api_request_Id to allow NULL values');
          await db.query(`ALTER TABLE api_request_history MODIFY COLUMN api_request_Id INT NULL`);
        }
      } catch (error) {
        console.error('Error checking/modifying column:', error);
      }
    }
  } catch (error) {
    console.error('Error creating/modifying api_request_history table:', error);
  }
};

// Initialize the table
createHistoryTable();

// Helper function to safely parse JSON fields
function parseJsonField(field) {
  if (!field) return {};

  // If field is already an object, return it
  if (typeof field === 'object' && field !== null) return field;

  // Try to parse the field as JSON
  try {
    return JSON.parse(field);
  } catch (error) {
    console.error('Error parsing JSON field:', error);
    console.log('Field value:', field);
    console.log('Field type:', typeof field);
    return {};
  }
}

class History {
  constructor(history) {
    this.id = history.api_request_history_Id;
    this.workspace_id = history.cms_workspace_Id;
    this.user_id = history.login_user_Id;
    this.request_id = history.api_request_Id;
    this.response = history.api_request_history_Response;
    this.status_code = history.api_request_history_Status_Code;
    this.response_time = history.api_request_history_Response_Time;
    this.is_active = history.is_active;
    this.is_delete = history.is_delete;
    this.created_at = history.created_at;
    this.updated_at = history.updated_at;
    this.deleted_at = history.deleted_at;
  }

  // Test database connection
  static async testConnection() {
    try {
      const [result] = await db.query('SELECT 1 as test');

      // Check if history table exists
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      const tableExists = tables.length > 0;

      // Create the table if it doesn't exist
      if (!tableExists) {
        await createHistoryTable();
      }

      return {
        success: true,
        result,
        historyTableExists: tableExists || tables.length > 0
      };
    } catch (error) {
      console.error('Database connection test failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Create a new history entry
  static async create(historyData) {
    try {
      console.log('Creating history entry with data:', {
        user_id: historyData.user_id,
        request_id: historyData.request_id,
        method: historyData.method,
        url: historyData.url
      });

      // Ensure the history table exists
      await createHistoryTable();

      // Validate request_id
      let requestId = null;
      if (historyData.request_id) {
        // Check if the request exists in the database
        try {
          const [requests] = await db.query('SELECT api_request_Id FROM api_request WHERE api_request_Id = ?', [historyData.request_id]);
          if (requests.length > 0) {
            requestId = historyData.request_id;
            console.log('Request found in database, using request_id:', requestId);
          } else {
            console.log('Request not found in database, will create a dummy request');
            // Will create a dummy request below
          }
        } catch (error) {
          console.error('Error checking request existence:', error);
          // Will create a dummy request below
        }
      }

      // If no valid request_id, create a dummy request
      if (!requestId) {
        try {
          console.log('Creating a dummy request for history entry');
          // First, check the structure of the api_request table
          const [columns] = await db.query(`
            SHOW COLUMNS FROM api_request
          `);

          console.log('api_request table columns:', columns.map(col => col.Field));

          // Map column names to our data fields
          const columnMap = {
            'api_request_Name': historyData.name || historyData.url || 'Unnamed Request',
            'api_request_Url': historyData.url || '',
            'api_request_Method': historyData.method || 'GET',
            'api_request_Headers': JSON.stringify(historyData.headers || {}),
            'api_request_Body': JSON.stringify(historyData.body || {}),
            'api_request_Query_Params': JSON.stringify(historyData.params || {}),
            'api_request_Auth_Type': historyData.auth ? JSON.stringify(historyData.auth.type || null) : null,
            'cms_workspace_Id': historyData.workspace_id || 123,
            'login_user_Id': historyData.user_id || 1,
            'is_active': 1,
            'is_delete': 0
          };

          // Build a dynamic query based on the actual columns in the table
          const availableColumns = columns.map(col => col.Field)
            .filter(col => col !== 'api_request_Id' && col !== 'created_at' && col !== 'updated_at' && col !== 'deleted_at')
            .filter(col => columnMap[col] !== undefined);

          const columnNames = availableColumns.join(', ');
          const placeholders = availableColumns.map(() => '?').join(', ');
          const values = availableColumns.map(col => columnMap[col]);

          console.log('Using columns for api_request insert:', availableColumns);

          // Execute the dynamic query
          const [result] = await db.query(`
            INSERT INTO api_request (
              ${columnNames}
            ) VALUES (${placeholders})
          `, values);

          requestId = result.insertId;
          console.log('Created dummy request with ID:', requestId);
        } catch (createError) {
          console.error('Error creating dummy request:', createError);
          // Continue with null request_id
        }
      }

      // Prepare response data
      let responseData = {};
      if (historyData.response_body || historyData.response_headers || historyData.status_code) {
        responseData = {
          body: historyData.response_body || {},
          headers: historyData.response_headers || {},
          status_code: historyData.status_code || 0,
          response_time: historyData.response_time || 0
        };
      }

      // Prepare request data
      const requestData = {
        method: historyData.method || 'GET',
        url: historyData.url || '',
        headers: historyData.headers || {},
        body: historyData.body || {},
        params: historyData.params || {},
        auth: historyData.auth || null
      };

      // Combine request and response data
      const combinedData = {
        request: requestData,
        response: responseData
      };

      // Safely stringify the combined data
      let jsonData;
      try {
        jsonData = JSON.stringify(combinedData);
      } catch (error) {
        console.error('Error stringifying history data:', error);
        jsonData = JSON.stringify({
          request: {
            method: historyData.method || 'GET',
            url: historyData.url || ''
          },
          response: {}
        });
      }

      // Prepare the query based on whether we have a valid request ID
      let query;

      if (requestId) {
        // If we have a valid request ID, use it
        query = `
          INSERT INTO api_request_history (
            cms_workspace_Id,
            login_user_Id,
            api_request_Id,
            api_request_history_Response,
            api_request_history_Status_Code,
            api_request_history_Response_Time,
            is_active,
            is_delete
          ) VALUES (?, ?, ?, ?, ?, ?, 1, 0)
        `;
      } else {
        // If we don't have a valid request ID, omit it from the query
        query = `
          INSERT INTO api_request_history (
            cms_workspace_Id,
            login_user_Id,
            api_request_history_Response,
            api_request_history_Status_Code,
            api_request_history_Response_Time,
            is_active,
            is_delete
          ) VALUES (?, ?, ?, ?, ?, 1, 0)
        `;
      }

      // Ensure user_id is valid
      let userId;

      if (!historyData.user_id) {
        console.warn('No user_id provided for history entry, using default user ID 1');
        userId = 1; // Default user ID if none provided
      } else {
        // Convert user_id to integer if it's a string
        userId = typeof historyData.user_id === 'string' ? parseInt(historyData.user_id) : historyData.user_id;

        if (isNaN(userId)) {
          console.warn('Invalid user_id:', historyData.user_id, 'using default user ID 1');
          userId = 1; // Default user ID if invalid
        }
      }

      console.log('Using user_id for history entry:', userId);

      // Prepare values based on the query
      let values;

      if (requestId) {
        // If we have a valid request ID, include it in the values
        values = [
          historyData.workspace_id || 123,
          userId,
          requestId,
          jsonData,
          historyData.status_code || null,
          historyData.response_time || null
        ];
      } else {
        // If we don't have a valid request ID, omit it from the values
        values = [
          historyData.workspace_id || 123,
          userId,
          jsonData,
          historyData.status_code || null,
          historyData.response_time || null
        ];
      }

      console.log('Using query:', query);
      console.log('Using values:', values);

      const [result] = await db.query(query, values);
      console.log('History entry created with ID:', result.insertId, 'and request_id:', requestId);

      return {
        id: result.insertId,
        user_id: historyData.user_id,
        request_id: requestId,
        method: historyData.method,
        url: historyData.url,
        status_code: historyData.status_code,
        response_time: historyData.response_time,
        is_active: 1,
        is_delete: 0
      };
    } catch (error) {
      console.error('Error creating history entry:', error);
      throw error;
    }
  }

  // Get history entries by user ID
  static async findByUserId(userId, workspaceId = 123, limit = 50) {
    try {
      // Ensure the history table exists
      await createHistoryTable();

      // Check if the table exists before querying
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      if (tables.length === 0) {
        console.log('api_request_history table does not exist, returning empty array');
        return [];
      }

      const query = `
        SELECT h.*, r.api_request_Name, r.api_request_Url, r.api_request_Method
        FROM api_request_history h
        LEFT JOIN api_request r ON h.api_request_Id = r.api_request_Id
        WHERE h.login_user_Id = ? AND h.cms_workspace_Id = ? AND h.is_active = 1 AND h.is_delete = 0
        ORDER BY h.created_at DESC
        LIMIT ?
      `;

      const [results] = await db.query(query, [userId, workspaceId, limit]);

      // Parse JSON fields
      const history = results.map(item => {
        return {
          ...item,
          response: parseJsonField(item.api_request_history_Response)
        };
      });

      return history;
    } catch (error) {
      console.error('Error finding history by user ID:', error);
      throw error;
    }
  }

  // Get history entries by request ID
  static async findByRequestId(requestId, userId, workspaceId = 123, limit = 50) {
    try {
      // Ensure the history table exists
      await createHistoryTable();

      // Check if the table exists before querying
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      if (tables.length === 0) {
        console.log('api_request_history table does not exist, returning empty array');
        return [];
      }

      const query = `
        SELECT h.*, r.api_request_Name, r.api_request_Url, r.api_request_Method
        FROM api_request_history h
        LEFT JOIN api_request r ON h.api_request_Id = r.api_request_Id
        WHERE h.api_request_Id = ? AND h.login_user_Id = ? AND h.cms_workspace_Id = ?
        AND h.is_active = 1 AND h.is_delete = 0
        ORDER BY h.created_at DESC
        LIMIT ?
      `;

      const [results] = await db.query(query, [requestId, userId, workspaceId, limit]);

      // Parse JSON fields
      const history = results.map(item => {
        return {
          ...item,
          response: parseJsonField(item.api_request_history_Response)
        };
      });

      return history;
    } catch (error) {
      console.error('Error finding history by request ID:', error);
      throw error;
    }
  }

  // Get a single history entry by ID
  static async findById(id) {
    try {
      // Ensure the history table exists
      await createHistoryTable();

      // Check if the table exists before querying
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      if (tables.length === 0) {
        console.log('api_request_history table does not exist, returning null');
        return null;
      }

      const query = `
        SELECT h.*, r.api_request_Name, r.api_request_Url, r.api_request_Method
        FROM api_request_history h
        LEFT JOIN api_request r ON h.api_request_Id = r.api_request_Id
        WHERE h.api_request_history_Id = ? AND h.is_active = 1 AND h.is_delete = 0
      `;
      const [results] = await db.query(query, [id]);

      if (results.length === 0) {
        return null;
      }

      // Parse JSON fields
      const item = results[0];
      const history = {
        ...item,
        response: parseJsonField(item.api_request_history_Response)
      };

      return history;
    } catch (error) {
      console.error('Error finding history by ID:', error);
      throw error;
    }
  }

  // Soft delete a history entry
  static async delete(id) {
    try {
      // Ensure the history table exists
      await createHistoryTable();

      // Check if the table exists before querying
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      if (tables.length === 0) {
        console.log('api_request_history table does not exist, returning success anyway');
        return { message: 'History entry deleted (table does not exist)', id, is_delete: 1 };
      }

      const query = 'UPDATE api_request_history SET is_delete = 1 WHERE api_request_history_Id = ?';
      await db.query(query, [id]);
      return { message: 'History entry deleted', id, is_delete: 1 };
    } catch (error) {
      console.error('Error deleting history entry:', error);
      throw error;
    }
  }

  // Hard delete a history entry (for admin use only)
  static async hardDelete(id) {
    try {
      const query = 'DELETE FROM api_request_history WHERE api_request_history_Id = ?';
      await db.query(query, [id]);
      return { message: 'History entry permanently deleted', id };
    } catch (error) {
      console.error('Error hard deleting history entry:', error);
      throw error;
    }
  }

  // Soft clear all history for a user
  static async clearByUserId(userId, workspaceId = 123) {
    try {
      // Ensure the history table exists
      await createHistoryTable();

      // Check if the table exists before querying
      const [tables] = await db.query("SHOW TABLES LIKE 'api_request_history'");
      if (tables.length === 0) {
        console.log('api_request_history table does not exist, returning success anyway');
        return { message: 'History cleared (table does not exist)', userId, workspaceId };
      }

      const query = 'UPDATE api_request_history SET is_delete = 1 WHERE login_user_Id = ? AND cms_workspace_Id = ?';
      await db.query(query, [userId, workspaceId]);
      return { message: 'History cleared', userId, workspaceId };
    } catch (error) {
      console.error('Error clearing history for user:', error);
      throw error;
    }
  }

  // Hard clear all history for a user (for admin use only)
  static async hardClearByUserId(userId, workspaceId = 123) {
    try {
      const query = 'DELETE FROM api_request_history WHERE login_user_Id = ? AND cms_workspace_Id = ?';
      await db.query(query, [userId, workspaceId]);
      return { message: 'History permanently cleared', userId, workspaceId };
    } catch (error) {
      console.error('Error hard clearing history for user:', error);
      throw error;
    }
  }
}

module.exports = History;
