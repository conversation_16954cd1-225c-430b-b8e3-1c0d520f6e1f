const db = require('../config/db');

class Request {
  static async findByCollectionId(collectionId) {
    const [rows] = await db.query(`
      SELECT r.*
      FROM api_request r
      JOIN collection_request_mapping m ON r.api_request_Id = m.api_request_Id
      WHERE m.collection_Id = ? AND m.collection_folder_Id IS NULL
      AND r.is_active = 1 AND r.is_delete = 0 AND m.is_active = 1 AND m.is_delete = 0
      ORDER BY m.collection_request_mapping_Order ASC
    `, [collectionId]);
    return rows;
  }

  static async findByFolderId(folderId) {
    const [rows] = await db.query(`
      SELECT r.*
      FROM api_request r
      JOIN collection_request_mapping m ON r.api_request_Id = m.api_request_Id
      WHERE m.collection_folder_Id = ?
      AND r.is_active = 1 AND r.is_delete = 0 AND m.is_active = 1 AND m.is_delete = 0
      ORDER BY m.collection_request_mapping_Order ASC
    `, [folderId]);
    return rows;
  }

  static async findById(id) {
    const [rows] = await db.query(
      'SELECT * FROM api_request WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
      [id]
    );
    return rows[0];
  }

  static async findByIdIncludeInactive(id) {
    const [rows] = await db.query('SELECT * FROM api_request WHERE api_request_Id = ?', [id]);
    return rows[0];
  }

  static async create(requestData) {
    const {
      name,
      url,
      method,
      headers,
      body,
      params,
      auth_type,
      workspace_id = 123,
      user_id,
      collection_id,
      folder_id,
      order = null
    } = requestData;

    // Begin transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // If folder_id is provided but collection_id is not, get the collection_id from the folder
      let effectiveCollectionId = collection_id;

      if (folder_id && !effectiveCollectionId) {
        // Get the collection_id from the folder
        const [folderRows] = await connection.query(
          'SELECT collection_Id FROM collection_folder WHERE collection_folder_Id = ?',
          [folder_id]
        );

        if (folderRows.length > 0) {
          effectiveCollectionId = folderRows[0].collection_Id;
        } else {
          throw new Error('Folder not found or does not have a collection_Id');
        }
      }

      if (!effectiveCollectionId) {
        throw new Error('Collection ID is required');
      }

      // Insert into api_request table
      const [requestResult] = await connection.query(
        `INSERT INTO api_request (
          api_request_Name,
          api_request_Url,
          api_request_Method,
          api_request_Headers,
          api_request_Body,
          api_request_Query_Params,
          api_request_Auth_Type,
          cms_workspace_Id,
          login_user_Id,
          is_active,
          is_delete
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0)`,
        [
          name,
          url,
          method,
          typeof headers === 'string' ? headers : JSON.stringify(headers || {}),
          typeof body === 'string' ? body : JSON.stringify(body || {}),
          typeof params === 'string' ? params : JSON.stringify(params || {}),
          auth_type,
          workspace_id,
          user_id
        ]
      );

      const requestId = requestResult.insertId;

      // Insert into collection_request_mapping table
      await connection.query(
        `INSERT INTO collection_request_mapping (
          collection_Id,
          collection_folder_Id,
          api_request_Id,
          collection_request_mapping_Order,
          is_active,
          is_delete
        ) VALUES (?, ?, ?, ?, 1, 0)`,
        [effectiveCollectionId, folder_id, requestId, order]
      );

      // Commit transaction
      await connection.commit();
      connection.release();

      return {
        id: requestId,
        name,
        url,
        method,
        headers: headers || {},
        body: body || {},
        params: params || {},
        auth_type,
        workspace_id,
        user_id,
        collection_id: effectiveCollectionId,
        folder_id,
        order,
        is_active: 1,
        is_delete: 0
      };
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      connection.release();
      throw error;
    }
  }

  static async update(id, requestData) {
    const {
      name,
      url,
      method,
      headers,
      body,
      params,
      auth_type,
      collection_id,
      folder_id,
      order
    } = requestData;

    // Begin transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Update api_request table
      await connection.query(
        `UPDATE api_request SET
          api_request_Name = ?,
          api_request_Url = ?,
          api_request_Method = ?,
          api_request_Headers = ?,
          api_request_Body = ?,
          api_request_Query_Params = ?,
          api_request_Auth_Type = ?
        WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0`,
        [
          name,
          url,
          method,
          typeof headers === 'string' ? headers : JSON.stringify(headers || {}),
          typeof body === 'string' ? body : JSON.stringify(body || {}),
          typeof params === 'string' ? params : JSON.stringify(params || {}),
          auth_type,
          id
        ]
      );

      // If collection_id or folder_id is provided, update the mapping
      if (collection_id || folder_id !== undefined || order !== undefined) {
        // Get current mapping
        const [mappings] = await connection.query(
          'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
          [id]
        );

        if (mappings.length > 0) {
          const mapping = mappings[0];
          let updateQuery = 'UPDATE collection_request_mapping SET ';
          const updateParams = [];
          const updateFields = [];

          if (collection_id) {
            updateFields.push('collection_Id = ?');
            updateParams.push(collection_id);
          }

          if (folder_id !== undefined) {
            updateFields.push('collection_folder_Id = ?');
            updateParams.push(folder_id);
          }

          if (order !== undefined) {
            updateFields.push('collection_request_mapping_Order = ?');
            updateParams.push(order);
          }

          if (updateFields.length > 0) {
            updateQuery += updateFields.join(', ');
            updateQuery += ' WHERE collection_request_mapping_Id = ?';
            updateParams.push(mapping.collection_request_mapping_Id);

            await connection.query(updateQuery, updateParams);
          }
        }
      }

      // Commit transaction
      await connection.commit();
      connection.release();

      // Get the updated request with its mapping
      const [requestRows] = await db.query(
        'SELECT * FROM api_request WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
        [id]
      );

      const [mappingRows] = await db.query(
        'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
        [id]
      );

      const request = requestRows[0];
      const mapping = mappingRows[0] || {};

      return {
        id,
        name,
        url,
        method,
        headers: headers || {},
        body: body || {},
        params: params || {},
        auth_type,
        collection_id: mapping.collection_Id,
        folder_id: mapping.collection_folder_Id,
        order: mapping.collection_request_mapping_Order,
        is_active: 1,
        is_delete: 0
      };
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      connection.release();
      throw error;
    }
  }

  static async delete(id) {
    // Soft delete - update is_delete to 1
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Soft delete the request
      await connection.query(
        'UPDATE api_request SET is_delete = 1 WHERE api_request_Id = ?',
        [id]
      );

      // Soft delete the mapping
      await connection.query(
        'UPDATE collection_request_mapping SET is_delete = 1 WHERE api_request_Id = ?',
        [id]
      );

      await connection.commit();
      connection.release();

      return { id, is_delete: 1 };
    } catch (error) {
      await connection.rollback();
      connection.release();
      throw error;
    }
  }

  static async hardDelete(id) {
    // Hard delete - actually remove from database (for admin use only)
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Delete the mapping first (due to foreign key constraints)
      await connection.query(
        'DELETE FROM collection_request_mapping WHERE api_request_Id = ?',
        [id]
      );

      // Delete the request
      await connection.query(
        'DELETE FROM api_request WHERE api_request_Id = ?',
        [id]
      );

      await connection.commit();
      connection.release();

      return { id };
    } catch (error) {
      await connection.rollback();
      connection.release();
      throw error;
    }
  }

  static async duplicate(id) {
    try {
      // Get the request to duplicate
      const request = await this.findById(id);
      if (!request) {
        throw new Error('Request not found');
      }

      // Get the mapping
      const [mappings] = await db.query(
        'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
        [id]
      );

      const mapping = mappings[0];
      if (!mapping) {
        throw new Error('Request mapping not found');
      }

      // Create a copy with a new name
      const newName = `${request.api_request_Name} (Copy)`;

      console.log('Duplicating request:', {
        id,
        name: request.api_request_Name,
        newName,
        mapping
      });

      // Create a new request with the same data
      const newRequest = await this.create({
        name: newName,
        url: request.api_request_Url,
        method: request.api_request_Method,
        headers: {},
        body: {},
        params: {},
        auth_type: request.api_request_Auth_Type,
        workspace_id: request.cms_workspace_Id || 123,
        user_id: request.login_user_Id || 1,
        collection_id: mapping.collection_Id,
        folder_id: mapping.collection_folder_Id,
        order: mapping.collection_request_mapping_Order
      });

      console.log('Created duplicated request:', newRequest);

      return newRequest;
    } catch (error) {
      console.error('Error in duplicate method:', error);
      throw error;
    }
  }

  static async rename(id, newName) {
    // Update the request name
    const [result] = await db.query(
      'UPDATE api_request SET api_request_Name = ? WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
      [newName, id]
    );

    console.log('Rename result:', result);

    if (result.affectedRows === 0) {
      throw new Error('Request not found or could not be updated');
    }

    // Get the updated request
    const request = await this.findById(id);

    if (!request) {
      throw new Error('Request not found after update');
    }

    // Get the mapping to include collection_id and folder_id
    // const [mappings] = await db.query(
    //   'SELECT * FROM collection_request_mapping WHERE api_request_Id = ? AND is_active = 1 AND is_delete = 0',
    //   [id]
    // );

    // const mapping = mappings[0] || {};

    // // Parse JSON fields safely
    // const safeJsonParse = (jsonString, defaultValue = {}) => {
    //   if (!jsonString) return defaultValue;
    //   try {
    //     return typeof jsonString === 'object' ? jsonString : JSON.parse(jsonString);
    //   } catch (error) {
    //     console.error('Error parsing JSON:', error, 'Value:', jsonString);
    //     return defaultValue;
    //   }
    // };

    // Return a complete request object
    return {
      id: request.api_request_Id,
      name: request.api_request_Name,
      url: request.api_request_Url,
      method: request.api_request_Method,
      headers: safeJsonParse(request.api_request_Headers, {}),
      body: safeJsonParse(request.api_request_Body, {}),
      params: safeJsonParse(request.api_request_Query_Params, {}),
      auth_type: request.api_request_Auth_Type,
      collection_id: mapping.collection_Id,
      folder_id: mapping.collection_folder_Id,
      order: mapping.collection_request_mapping_Order,
      is_active: request.is_active,
      is_delete: request.is_delete
    };
  }
}

module.exports = Request;