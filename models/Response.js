const db = require('../config/db');
const fs = require('fs');
const path = require('path');

// Create responses table if it doesn't exist
const createResponsesTable = async () => {
  try {
    // Check if table exists
    const [tables] = await db.query("SHOW TABLES LIKE 'responses'");
    if (tables.length === 0) {
      console.log('Creating responses table...');

      // Create the table
      await db.query(`
        CREATE TABLE IF NOT EXISTS \`responses\` (
          \`id\` INT NOT NULL AUTO_INCREMENT,
          \`request_id\` INT NOT NULL,
          \`status_code\` INT NULL,
          \`headers\` TEXT NULL,
          \`body\` LONGTEXT NULL,
          \`response_time\` INT NULL,
          \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (\`id\`),
          INDEX \`request_id_idx\` (\`request_id\` ASC)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);

      console.log('Responses table created successfully');
    }
  } catch (error) {
    console.error('Error creating responses table:', error);
  }
};

// Initialize the table
createResponsesTable();

class Response {
  static async findByRequestId(requestId) {
    try {
      // Ensure table exists
      await createResponsesTable();

      const [rows] = await db.query('SELECT * FROM responses WHERE request_id = ? ORDER BY created_at DESC', [requestId]);
      return rows;
    } catch (error) {
      console.error('Error finding responses by request ID:', error);
      return []; // Return empty array on error
    }
  }

  static async create(responseData) {
    try {
      // Ensure table exists
      await createResponsesTable();

      const { request_id, status_code, headers, body, response_time } = responseData;

      // Safely stringify JSON data
      const headersJson = typeof headers === 'object' ? JSON.stringify(headers || {}) : '{}';
      const bodyJson = typeof body === 'object' ? JSON.stringify(body || {}) :
                      (typeof body === 'string' ? `"${body}"` : '{}');

      const [result] = await db.query(
        'INSERT INTO responses (request_id, status_code, headers, body, response_time) VALUES (?, ?, ?, ?, ?)',
        [request_id, status_code, headersJson, bodyJson, response_time]
      );

      return { id: result.insertId, request_id, status_code, headers, body, response_time };
    } catch (error) {
      console.error('Error creating response:', error);
      // Return a minimal response object on error
      return {
        id: 0,
        request_id: responseData.request_id,
        status_code: responseData.status_code,
        error: error.message
      };
    }
  }

  static async delete(id) {
    try {
      // Ensure table exists
      await createResponsesTable();

      await db.query('DELETE FROM responses WHERE id = ?', [id]);
      return { id };
    } catch (error) {
      console.error('Error deleting response:', error);
      return { id, error: error.message };
    }
  }

  static async deleteByRequestId(requestId) {
    try {
      // Ensure table exists
      await createResponsesTable();

      await db.query('DELETE FROM responses WHERE request_id = ?', [requestId]);
      return { requestId };
    } catch (error) {
      console.error('Error deleting responses by request ID:', error);
      return { requestId, error: error.message };
    }
  }
}

module.exports = Response;