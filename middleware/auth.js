const jwt = require('jsonwebtoken');

module.exports = (req, res, next) => {
  // Get token from header
  const token = req.header('x-auth-token');

  console.log('Auth middleware called for path:', req.path);
  console.log('Token exists:', !!token);

  // Check if no token
  if (!token) {
    console.log('No token provided');
    return res.status(401).json({ status: 'Failure', message: 'No token, authorization denied' });
  }

  try {
    // Verify token with a more robust approach
    const secret = process.env.JWT_SECRET || 'your-secret-key';

    // Log token details for debugging (without revealing the full token)
    console.log('Token length:', token.length);
    console.log('Token prefix:', token.substring(0, 10) + '...');

    let decoded;
    try {
      decoded = jwt.verify(token, secret);
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError.message);

      // Try to decode without verification to see what's in the token
      try {
        const decodedWithoutVerify = jwt.decode(token);
        console.log('Token decoded without verification:', decodedWithoutVerify);
      } catch (decodeError) {
        console.error('Token decode error:', decodeError.message);
      }

      throw jwtError;
    }

    console.log('Token decoded successfully:', decoded);

    // Check if decoded token has the expected structure
    if (!decoded || !decoded.id) {
      throw new Error('Invalid token structure');
    }

    // Add user from payload
    // Ensure the ID is a number
    const userId = typeof decoded.id === 'string' ? parseInt(decoded.id) : decoded.id;

    if (isNaN(userId)) {
      console.error('Invalid user ID in token:', decoded.id);
      throw new Error('Invalid user ID in token');
    }

    req.user = { id: userId };
    console.log('User ID set:', req.user.id, 'Type:', typeof req.user.id);
    next();
  } catch (error) {
    console.error('Token verification error:', error.message);
    return res.status(401).json({
      status: 'Failure',
      message: 'Token is not valid',
      error: error.message
    });
  }
};