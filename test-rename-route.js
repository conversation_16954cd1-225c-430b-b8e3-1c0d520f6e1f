const axios = require('axios');

// Test the rename route change from /api/requests/{id}/rename to /api/requests/rename/{id}
async function testRenameRoute() {
  const baseUrl = 'http://localhost:3000'; // Adjust port if needed
  
  console.log('Testing rename route change...\n');

  try {
    // First, create a test request to rename
    console.log('Step 1: Creating a test request');
    const createResponse = await axios.post(`${baseUrl}/api/test/create-request`, {
      name: 'Test Request for Rename',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      method: 'GET',
      collection_id: 1
    });

    if (!createResponse.data.success) {
      throw new Error('Failed to create test request: ' + createResponse.data.message);
    }

    const requestId = createResponse.data.data.id;
    console.log('✅ Created test request with ID:', requestId);

    // Test the new rename route format
    console.log('\nStep 2: Testing new rename route format /api/requests/rename/{id}');
    
    // Note: This test assumes you have authentication set up
    // You may need to adjust the headers based on your auth implementation
    const renameResponse = await axios.post(`${baseUrl}/api/requests/rename/${requestId}`, {
      name: 'Renamed Test Request'
    }, {
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers if needed
        // 'x-auth-token': 'your-token-here'
      }
    });

    console.log('✅ New rename route works! Response:', {
      success: renameResponse.data.success,
      message: renameResponse.data.message,
      status: renameResponse.status
    });

    // Test that the old route format no longer works
    console.log('\nStep 3: Verifying old route format /api/requests/{id}/rename no longer works');
    
    try {
      await axios.post(`${baseUrl}/api/requests/${requestId}/rename`, {
        name: 'This Should Fail'
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('❌ Old route still works - this should not happen!');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Old route correctly returns 404 - route change successful!');
      } else {
        console.log('⚠️  Old route failed with unexpected error:', error.response?.status || error.message);
      }
    }

    console.log('\n🎉 Route change test completed successfully!');
    console.log('📝 Summary:');
    console.log('   - New route /api/requests/rename/{id} works correctly');
    console.log('   - Old route /api/requests/{id}/rename is no longer available');
    console.log('   - Frontend code has been updated to use the new route format');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    console.log('\n📝 Note: If you get authentication errors, make sure to:');
    console.log('   1. Start the server: npm start');
    console.log('   2. Create a user account if needed');
    console.log('   3. Update the test with proper authentication headers');
  }
}

// Run the test
if (require.main === module) {
  testRenameRoute();
}

module.exports = testRenameRoute;
