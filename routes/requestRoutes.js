const express = require('express');
const router = express.Router();
const {
  createRequest,
  getRequest,
  updateRequest,
  deleteRequest,
  executeRequest,
  renameRequest,
  duplicateRequest
} = require('../controllers/requestController');
const auth = require('../middleware/auth');

// CRUD operations
router.post('/', auth, createRequest);
router.get('/:id', auth, getRequest);
router.put('/:id', auth, updateRequest);
router.delete('/:id', auth, deleteRequest);

// Execute request
router.post('/:id/execute', auth, executeRequest);

// Additional operations
router.post('/rename/:id', auth, renameRequest);
router.post('/:id/duplicate', auth, duplicateRequest);

module.exports = router;