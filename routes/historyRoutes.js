const express = require('express');
const router = express.Router();
const historyController = require('../controllers/historyController');
const auth = require('../middleware/auth');

// Get all history for the authenticated user (GET method - deprecated but kept for backward compatibility)
router.get('/', auth, historyController.getHistory);

// Get history with filters (POST method - new recommended way)
router.post('/filter', auth, historyController.getHistory);

// Add a request to history (authenticated)
router.post('/', auth, historyController.addToHistory);

// Add a request to history (no auth required - direct access)
router.post('/direct', historyController.addToHistory);

// Get history for a specific request
router.get('/request/:requestId', auth, historyController.getHistoryByRequestId);

// Get a single history entry
router.get('/:id', auth, historyController.getHistoryById);

// Delete a history entry
router.delete('/:id', auth, historyController.deleteHistory);

// Clear all history for the authenticated user
router.delete('/', auth, historyController.clearHistory);

module.exports = router;
