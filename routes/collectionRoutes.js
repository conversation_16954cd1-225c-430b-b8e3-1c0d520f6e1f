const express = require('express');
const router = express.Router();
const {
  getCollections,
  getCollection,
  createCollection,
  updateCollection,
  deleteCollection
} = require('../controllers/collectionController');
const auth = require('../middleware/auth');

// New POST endpoint for collections list
router.post('/list', auth, getCollections);

// New POST endpoint for updating collections
router.post('/update/:id', auth, updateCollection);

// Keep GET endpoint for backward compatibility (deprecated)
router.get('/', auth, getCollections);
router.get('/:id', auth, getCollection);
router.post('/', auth, createCollection);
// Keep PUT endpoint for backward compatibility (deprecated)
router.put('/:id', auth, updateCollection);
router.delete('/:id', auth, deleteCollection);

module.exports = router;
