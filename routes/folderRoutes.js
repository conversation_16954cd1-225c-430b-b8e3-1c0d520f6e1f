const express = require('express');
const router = express.Router();
const {
  getFolders,
  getFolder,
  createFolder,
  updateFolder,
  deleteFolder
} = require('../controllers/folderController');
const auth = require('../middleware/auth');

router.get('/', auth, getFolders);
router.get('/:id', auth, getFolder);
router.post('/', auth, createFolder);
// New POST endpoint for updating folders
router.post('/update/:id', auth, updateFolder);
// Keep PUT endpoint for backward compatibility (deprecated)
router.put('/:id', auth, updateFolder);
// New POST endpoint for deleting folders
router.post('/delete', auth, deleteFolder);
// Keep DELETE endpoint for backward compatibility (deprecated)
router.delete('/:id', auth, deleteFolder);

module.exports = router;
