const mysql = require('mysql2');
require('dotenv').config();

const pool = mysql.createPool({
//   host: process.env.DB_HOST || 'localhost',
//   user: process.env.DB_USER || 'root',
//   password: process.env.DB_PASSWORD || 'Rohan1qaz2wsx#@',
//   database: process.env.DB_NAME || 'redoq_kuick_api',
//   waitForConnections: true,
//   connectionLimit: 10,
//   queueLimit: 0
    host: 'localhost',
    user: 'root',
    password: 'admin1234',
    database: 'redoq_kuick_api',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

const promisePool = pool.promise();

module.exports = promisePool;